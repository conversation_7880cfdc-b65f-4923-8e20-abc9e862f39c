@page "/management-trainee"
@layout CareerPortal.Components.Layout.CareerPortalLayout

<PageTitle>Management Trainee Program - Pak Suzuki Career Portal</PageTitle>

<!-- Hero Section -->
<section class="hero">
    <div class="hero-content">
        <h1>Management Trainee Program</h1>
        <p>Fast-track your leadership journey with our comprehensive management development program. Join Pakistan's premier automotive company and accelerate your career growth.</p>
        <a href="#apply" class="cta-button">Apply Now</a>
    </div>
</section>

<!-- Program Overview Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Program Overview</h2>
        <p class="section-subtitle">A structured 18-month leadership development program designed to create future business leaders</p>

        <div class="overview-content">
            <div class="overview-text">
                <h3>Leadership Excellence Journey</h3>
                <p>Our Management Trainee Program is a comprehensive leadership development initiative that prepares high-potential graduates for senior management roles. Through structured rotations, mentorship, and challenging assignments, you'll develop the skills and experience needed to lead in the automotive industry.</p>
                
                <h4>Program Highlights:</h4>
                <ul class="highlight-list">
                    <li>18-month structured development program</li>
                    <li>Cross-functional department rotations</li>
                    <li>Executive mentorship and coaching</li>
                    <li>Leadership training workshops</li>
                    <li>Strategic project assignments</li>
                    <li>Fast-track career progression</li>
                </ul>
            </div>
            
            <div class="overview-stats">
                <div class="stat-item">
                    <div class="stat-number">18</div>
                    <div class="stat-label">Months Program</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">6</div>
                    <div class="stat-label">Department Rotations</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">95%</div>
                    <div class="stat-label">Promotion Rate</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100+</div>
                    <div class="stat-label">Training Hours</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Program Structure Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Program Structure</h2>
        <p class="section-subtitle">Comprehensive 18-month journey through key business functions</p>

        <div class="program-phases">
            <div class="phase-card">
                <div class="phase-header">
                    <div class="phase-number">Phase 1</div>
                    <h3>Foundation (Months 1-6)</h3>
                </div>
                <div class="phase-content">
                    <h4>Business Fundamentals</h4>
                    <p>Build strong foundation in automotive business operations and company culture.</p>
                    <ul class="phase-activities">
                        <li>Company orientation and culture immersion</li>
                        <li>Automotive industry overview</li>
                        <li>Business process understanding</li>
                        <li>Initial department rotation (2 departments)</li>
                    </ul>
                </div>
            </div>

            <div class="phase-card">
                <div class="phase-header">
                    <div class="phase-number">Phase 2</div>
                    <h3>Development (Months 7-12)</h3>
                </div>
                <div class="phase-content">
                    <h4>Skill Building & Leadership</h4>
                    <p>Develop core management skills and take on increasing responsibilities.</p>
                    <ul class="phase-activities">
                        <li>Advanced department rotations (3 departments)</li>
                        <li>Leadership training workshops</li>
                        <li>Project management assignments</li>
                        <li>Cross-functional team leadership</li>
                    </ul>
                </div>
            </div>

            <div class="phase-card">
                <div class="phase-header">
                    <div class="phase-number">Phase 3</div>
                    <h3>Specialization (Months 13-18)</h3>
                </div>
                <div class="phase-content">
                    <h4>Strategic Leadership</h4>
                    <p>Focus on chosen specialization and prepare for management role.</p>
                    <ul class="phase-activities">
                        <li>Specialized department focus</li>
                        <li>Strategic project leadership</li>
                        <li>Executive mentorship program</li>
                        <li>Management role preparation</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Department Rotations Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Department Rotations</h2>
        <p class="section-subtitle">Gain comprehensive business exposure through structured rotations</p>

        <div class="rotations-grid">
            <div class="rotation-card">
                <div class="rotation-icon">🏭</div>
                <h3>Manufacturing & Operations</h3>
                <p>Learn production processes, quality management, and operational efficiency in automotive manufacturing.</p>
                <div class="rotation-duration">Duration: 3 months</div>
            </div>

            <div class="rotation-card">
                <div class="rotation-icon">📈</div>
                <h3>Sales & Marketing</h3>
                <p>Understand market dynamics, customer relationships, and sales strategies in the automotive sector.</p>
                <div class="rotation-duration">Duration: 3 months</div>
            </div>

            <div class="rotation-card">
                <div class="rotation-icon">💰</div>
                <h3>Finance & Planning</h3>
                <p>Master financial analysis, budgeting, and strategic planning for business growth.</p>
                <div class="rotation-duration">Duration: 3 months</div>
            </div>

            <div class="rotation-card">
                <div class="rotation-icon">👥</div>
                <h3>Human Resources</h3>
                <p>Experience talent management, organizational development, and employee engagement strategies.</p>
                <div class="rotation-duration">Duration: 3 months</div>
            </div>

            <div class="rotation-card">
                <div class="rotation-icon">🔧</div>
                <h3>Engineering & R&D</h3>
                <p>Explore product development, innovation processes, and technical project management.</p>
                <div class="rotation-duration">Duration: 3 months</div>
            </div>

            <div class="rotation-card">
                <div class="rotation-icon">🎯</div>
                <h3>Strategic Planning</h3>
                <p>Participate in strategic initiatives, business development, and corporate planning activities.</p>
                <div class="rotation-duration">Duration: 3 months</div>
            </div>
        </div>
    </div>
</section>

<!-- Leadership Development Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Leadership Development</h2>
        <p class="section-subtitle">Comprehensive training modules to build essential leadership capabilities</p>

        <div class="leadership-modules">
            <div class="module-category">
                <h3>Core Leadership Skills</h3>
                <div class="modules-list">
                    <div class="module-item">
                        <h4>Strategic Thinking</h4>
                        <p>Develop long-term vision and strategic planning capabilities</p>
                    </div>
                    <div class="module-item">
                        <h4>Team Leadership</h4>
                        <p>Master team building, motivation, and performance management</p>
                    </div>
                    <div class="module-item">
                        <h4>Decision Making</h4>
                        <p>Learn analytical decision-making and problem-solving techniques</p>
                    </div>
                </div>
            </div>

            <div class="module-category">
                <h3>Business Acumen</h3>
                <div class="modules-list">
                    <div class="module-item">
                        <h4>Financial Management</h4>
                        <p>Understand financial principles and business economics</p>
                    </div>
                    <div class="module-item">
                        <h4>Market Analysis</h4>
                        <p>Analyze market trends and competitive landscapes</p>
                    </div>
                    <div class="module-item">
                        <h4>Operations Excellence</h4>
                        <p>Optimize processes and drive operational efficiency</p>
                    </div>
                </div>
            </div>

            <div class="module-category">
                <h3>Communication & Influence</h3>
                <div class="modules-list">
                    <div class="module-item">
                        <h4>Executive Communication</h4>
                        <p>Master presentation and executive communication skills</p>
                    </div>
                    <div class="module-item">
                        <h4>Stakeholder Management</h4>
                        <p>Build relationships and manage diverse stakeholders</p>
                    </div>
                    <div class="module-item">
                        <h4>Change Leadership</h4>
                        <p>Lead organizational change and transformation initiatives</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Success Stories Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Success Stories</h2>
        <p class="section-subtitle">Meet our alumni who have advanced to leadership positions</p>

        <div class="success-stories">
            <div class="story-card">
                <div class="story-header">
                    <h4>Sarah Ahmed</h4>
                    <span class="story-position">Regional Sales Manager</span>
                    <span class="story-year">MT Program 2020</span>
                </div>
                <p>"The Management Trainee Program gave me comprehensive exposure to all business functions. The mentorship and challenging projects prepared me for my current leadership role."</p>
                <div class="story-achievement">
                    <strong>Achievement:</strong> Promoted to Regional Sales Manager within 2 years
                </div>
            </div>

            <div class="story-card">
                <div class="story-header">
                    <h4>Muhammad Hassan</h4>
                    <span class="story-position">Operations Manager</span>
                    <span class="story-year">MT Program 2019</span>
                </div>
                <p>"The program's structured approach and cross-functional exposure helped me understand the automotive business holistically. I now lead a team of 50+ professionals."</p>
                <div class="story-achievement">
                    <strong>Achievement:</strong> Led cost optimization project saving PKR 100M annually
                </div>
            </div>

            <div class="story-card">
                <div class="story-header">
                    <h4>Fatima Khan</h4>
                    <span class="story-position">Finance Director</span>
                    <span class="story-year">MT Program 2018</span>
                </div>
                <p>"The program's emphasis on strategic thinking and financial acumen prepared me for senior finance roles. The networking opportunities were invaluable."</p>
                <div class="story-achievement">
                    <strong>Achievement:</strong> Youngest Finance Director in company history
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Requirements Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Eligibility & Requirements</h2>
        
        <div class="requirements-content">
            <div class="req-column">
                <h3>Academic Excellence</h3>
                <ul class="req-list">
                    <li>Bachelor's degree from recognized university</li>
                    <li>Minimum CGPA of 3.5 or equivalent</li>
                    <li>Business, Engineering, or related field preferred</li>
                    <li>Fresh graduate or maximum 2 years experience</li>
                </ul>
            </div>

            <div class="req-column">
                <h3>Leadership Potential</h3>
                <ul class="req-list">
                    <li>Demonstrated leadership experience</li>
                    <li>Strong analytical and problem-solving skills</li>
                    <li>Excellent communication abilities</li>
                    <li>High learning agility and adaptability</li>
                </ul>
            </div>

            <div class="req-column">
                <h3>Personal Attributes</h3>
                <ul class="req-list">
                    <li>High integrity and professional ethics</li>
                    <li>Results-oriented mindset</li>
                    <li>Cultural fit with company values</li>
                    <li>Commitment to 18-month program</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Application Section -->
<section class="section cta-section" id="apply">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Lead the Future?</h2>
            <p>Join our Management Trainee Program and fast-track your journey to becoming a business leader in Pakistan's automotive industry.</p>
            <div class="cta-buttons">
                <a href="/Account/Register" class="cta-button">Apply Now</a>
                <a href="/internships" class="cta-button-secondary">View Other Programs</a>
            </div>
        </div>
    </div>
</section>

<style>
    /* Base styles similar to other pages */
    .hero {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 80px 20px;
        text-align: center;
        position: relative;
        min-height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 800px;
    }

    .hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        color: white;
    }

    .hero p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-button {
        display: inline-block;
        background: #ff6b35;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    }

    .cta-button:hover {
        background: #e55a2b;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        color: white;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section {
        padding: 60px 0;
        width: 100%;
    }

    .section-title {
        text-align: center;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .section-subtitle {
        text-align: center;
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 50px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .overview-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 50px;
        align-items: start;
    }

    .overview-text h3 {
        font-size: 1.8rem;
        color: #1e3c72;
        margin-bottom: 20px;
    }

    .overview-text h4 {
        font-size: 1.3rem;
        color: #1e3c72;
        margin: 25px 0 15px 0;
    }

    .overview-text p {
        color: #666;
        line-height: 1.7;
        margin-bottom: 20px;
    }

    .highlight-list {
        list-style: none;
        padding-left: 0;
    }

    .highlight-list li {
        padding: 8px 0;
        color: #555;
        position: relative;
        padding-left: 25px;
    }

    .highlight-list li:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .overview-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .stat-item {
        text-align: center;
        padding: 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #ff6b35;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
    }

    .program-phases {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .phase-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .phase-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .phase-header {
        background: linear-gradient(135deg, #1e3c72, #2a5298);
        color: white;
        padding: 25px;
        text-align: center;
    }

    .phase-number {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 10px;
        display: inline-block;
    }

    .phase-header h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin: 0;
    }

    .phase-content {
        padding: 25px;
    }

    .phase-content h4 {
        font-size: 1.2rem;
        font-weight: 600;
        color: #1e3c72;
        margin-bottom: 10px;
    }

    .phase-content p {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .phase-activities {
        list-style: none;
        padding-left: 0;
    }

    .phase-activities li {
        padding: 5px 0;
        color: #555;
        position: relative;
        padding-left: 20px;
        font-size: 0.9rem;
    }

    .phase-activities li:before {
        content: "•";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .rotations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-top: 40px;
    }

    .rotation-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: 1px solid #f0f0f0;
    }

    .rotation-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(0,0,0,0.15);
    }

    .rotation-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 1.5rem;
        color: white;
    }

    .rotation-card h3 {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #1e3c72;
    }

    .rotation-card p {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .rotation-duration {
        background: #e8f4f8;
        color: #1e3c72;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
    }

    .leadership-modules {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 40px;
        margin-top: 40px;
    }

    .module-category h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 25px;
        color: #1e3c72;
        text-align: center;
    }

    .modules-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .module-item {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border-left: 4px solid #ff6b35;
    }

    .module-item h4 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e3c72;
        margin-bottom: 8px;
    }

    .module-item p {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.5;
        margin: 0;
    }

    .success-stories {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .story-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-top: 4px solid #1e3c72;
    }

    .story-header {
        margin-bottom: 15px;
    }

    .story-header h4 {
        font-size: 1.2rem;
        font-weight: 600;
        color: #1e3c72;
        margin-bottom: 5px;
    }

    .story-position {
        background: #e8f4f8;
        color: #1e3c72;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
        margin-right: 10px;
    }

    .story-year {
        color: #666;
        font-size: 0.9rem;
    }

    .story-card p {
        color: #555;
        line-height: 1.6;
        margin-bottom: 15px;
        font-style: italic;
    }

    .story-achievement {
        background: #f8fff9;
        color: #28a745;
        padding: 10px;
        border-radius: 5px;
        border-left: 3px solid #28a745;
        font-size: 0.9rem;
    }

    .requirements-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        margin-top: 40px;
    }

    .req-column h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .req-list {
        list-style: none;
        padding-left: 0;
    }

    .req-list li {
        padding: 10px 0;
        color: #555;
        position: relative;
        padding-left: 25px;
        line-height: 1.5;
    }

    .req-list li:before {
        content: "→";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .cta-section {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
    }

    .cta-content {
        text-align: center;
        max-width: 600px;
        margin: 0 auto;
    }

    .cta-content h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: white;
    }

    .cta-content p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .cta-button-secondary {
        display: inline-block;
        background: transparent;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        border: 2px solid white;
        transition: all 0.3s ease;
    }

    .cta-button-secondary:hover {
        background: white;
        color: #1e3c72;
    }

    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .hero {
            padding: 60px 15px;
            min-height: 350px;
        }
        
        .hero h1 {
            font-size: 2.5rem;
        }
        
        .section {
            padding: 40px 0;
        }
        
        .section-title {
            font-size: 2rem;
        }
        
        .overview-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .overview-stats {
            grid-template-columns: 1fr 1fr;
        }
        
        .program-phases {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .rotations-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .leadership-modules {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .success-stories {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .requirements-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .container {
            padding: 0 15px;
        }

        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }
    }

    @@media (max-width: 480px) {
        .hero h1 {
            font-size: 2rem;
        }
        
        .section-title {
            font-size: 1.8rem;
        }
        
        .overview-stats {
            grid-template-columns: 1fr;
        }

        .cta-content h2 {
            font-size: 2rem;
        }
    }
</style>
