@page "/internships/engineering"
@layout CareerPortal.Components.Layout.CareerPortalLayout

<PageTitle>Engineering Internship - Pak Suzuki Career Portal</PageTitle>

<!-- Hero Section -->
<section class="hero">
    <div class="hero-content">
        <h1>Engineering Internship Program</h1>
        <p>Join our engineering team and gain hands-on experience in automotive design, manufacturing, and innovation. Work on cutting-edge projects that shape the future of mobility in Pakistan.</p>
        <a href="#apply" class="cta-button">Apply Now</a>
    </div>
</section>

<!-- Program Overview Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Program Overview</h2>
        <p class="section-subtitle">Our Engineering Internship Program offers comprehensive exposure to automotive engineering across multiple disciplines</p>

        <div class="overview-content">
            <div class="overview-text">
                <h3>What You'll Experience</h3>
                <p>As an engineering intern at Pak Suzuki, you'll work alongside experienced engineers on real projects that directly impact our vehicle development and manufacturing processes. This program is designed to bridge the gap between academic learning and industry practice.</p>
                
                <h4>Key Highlights:</h4>
                <ul class="highlight-list">
                    <li>Work on actual vehicle development projects</li>
                    <li>Exposure to latest automotive technologies</li>
                    <li>Mentorship from senior engineers</li>
                    <li>Cross-functional team collaboration</li>
                    <li>Professional development workshops</li>
                    <li>Potential for full-time employment</li>
                </ul>
            </div>
            
            <div class="overview-stats">
                <div class="stat-item">
                    <div class="stat-number">12</div>
                    <div class="stat-label">Weeks Duration</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">5</div>
                    <div class="stat-label">Engineering Departments</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">85%</div>
                    <div class="stat-label">Conversion to Full-time</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Projects Annually</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Specialization Areas Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Specialization Areas</h2>
        <p class="section-subtitle">Choose your focus area based on your academic background and career interests</p>

        <div class="specialization-grid">
            <div class="specialization-card">
                <div class="spec-icon">🚗</div>
                <h3>Vehicle Design & Development</h3>
                <p>Work on vehicle styling, body engineering, and component design using CAD software and simulation tools.</p>
                <ul class="spec-features">
                    <li>3D Modeling & CAD Design</li>
                    <li>Finite Element Analysis</li>
                    <li>Prototype Development</li>
                    <li>Design Validation Testing</li>
                </ul>
            </div>

            <div class="specialization-card">
                <div class="spec-icon">⚙️</div>
                <h3>Manufacturing Engineering</h3>
                <p>Optimize production processes, improve efficiency, and ensure quality in our manufacturing operations.</p>
                <ul class="spec-features">
                    <li>Process Optimization</li>
                    <li>Lean Manufacturing</li>
                    <li>Automation Systems</li>
                    <li>Production Planning</li>
                </ul>
            </div>

            <div class="specialization-card">
                <div class="spec-icon">🔬</div>
                <h3>Quality Assurance</h3>
                <p>Ensure product quality through testing, inspection, and continuous improvement methodologies.</p>
                <ul class="spec-features">
                    <li>Quality Control Testing</li>
                    <li>Statistical Process Control</li>
                    <li>Failure Analysis</li>
                    <li>Compliance Standards</li>
                </ul>
            </div>

            <div class="specialization-card">
                <div class="spec-icon">🔋</div>
                <h3>Powertrain Engineering</h3>
                <p>Work on engine systems, transmission, and emerging electric vehicle technologies.</p>
                <ul class="spec-features">
                    <li>Engine Performance Testing</li>
                    <li>Transmission Systems</li>
                    <li>Emission Control</li>
                    <li>Hybrid/Electric Systems</li>
                </ul>
            </div>

            <div class="specialization-card">
                <div class="spec-icon">🛠️</div>
                <h3>Research & Development</h3>
                <p>Contribute to innovative projects and next-generation automotive technologies.</p>
                <ul class="spec-features">
                    <li>Technology Research</li>
                    <li>Innovation Projects</li>
                    <li>Patent Development</li>
                    <li>Future Mobility Solutions</li>
                </ul>
            </div>

            <div class="specialization-card">
                <div class="spec-icon">📊</div>
                <h3>Systems Engineering</h3>
                <p>Work on complex automotive systems integration and optimization.</p>
                <ul class="spec-features">
                    <li>Systems Integration</li>
                    <li>Performance Analysis</li>
                    <li>Requirements Engineering</li>
                    <li>System Validation</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Learning Outcomes Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Learning Outcomes</h2>
        <p class="section-subtitle">What you'll gain from our engineering internship program</p>

        <div class="outcomes-grid">
            <div class="outcome-item">
                <div class="outcome-icon">🎯</div>
                <h4>Technical Skills</h4>
                <p>Master industry-standard tools and technologies used in automotive engineering.</p>
            </div>

            <div class="outcome-item">
                <div class="outcome-icon">🤝</div>
                <h4>Professional Network</h4>
                <p>Build relationships with engineers, managers, and industry professionals.</p>
            </div>

            <div class="outcome-item">
                <div class="outcome-icon">📈</div>
                <h4>Project Management</h4>
                <p>Learn to manage engineering projects from concept to completion.</p>
            </div>

            <div class="outcome-item">
                <div class="outcome-icon">💡</div>
                <h4>Innovation Mindset</h4>
                <p>Develop creative problem-solving skills and innovative thinking.</p>
            </div>

            <div class="outcome-item">
                <div class="outcome-icon">🏆</div>
                <h4>Industry Knowledge</h4>
                <p>Gain deep understanding of automotive industry trends and challenges.</p>
            </div>

            <div class="outcome-item">
                <div class="outcome-icon">🎓</div>
                <h4>Career Readiness</h4>
                <p>Prepare for a successful career in automotive engineering.</p>
            </div>
        </div>
    </div>
</section>

<!-- Requirements Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Eligibility & Requirements</h2>
        
        <div class="requirements-content">
            <div class="req-column">
                <h3>Academic Requirements</h3>
                <ul class="req-list">
                    <li>Currently enrolled in Mechanical, Electrical, Industrial, or Automotive Engineering</li>
                    <li>Minimum CGPA of 3.2 or equivalent</li>
                    <li>Completed at least 4 semesters of engineering coursework</li>
                    <li>Available for 12-week full-time internship</li>
                </ul>
            </div>

            <div class="req-column">
                <h3>Technical Skills</h3>
                <ul class="req-list">
                    <li>Proficiency in CAD software (AutoCAD, SolidWorks, or similar)</li>
                    <li>Basic understanding of engineering principles</li>
                    <li>Familiarity with Microsoft Office Suite</li>
                    <li>Knowledge of programming languages (preferred)</li>
                </ul>
            </div>

            <div class="req-column">
                <h3>Personal Attributes</h3>
                <ul class="req-list">
                    <li>Strong analytical and problem-solving skills</li>
                    <li>Excellent communication abilities</li>
                    <li>Team collaboration mindset</li>
                    <li>Attention to detail and quality</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Application Section -->
<section class="section cta-section" id="apply">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Engineer Your Future?</h2>
            <p>Join our engineering internship program and work on projects that drive innovation in Pakistan's automotive industry.</p>
            <div class="cta-buttons">
                <a href="/Account/Register" class="cta-button">Apply Now</a>
                <a href="/internships" class="cta-button-secondary">View All Programs</a>
            </div>
        </div>
    </div>
</section>

<style>
    /* Inherit base styles from landing.css */
    .hero {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 80px 20px;
        text-align: center;
        position: relative;
        min-height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 800px;
    }

    .hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        color: white;
    }

    .hero p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-button {
        display: inline-block;
        background: #ff6b35;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    }

    .cta-button:hover {
        background: #e55a2b;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        color: white;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section {
        padding: 60px 0;
        width: 100%;
    }

    .section-title {
        text-align: center;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .section-subtitle {
        text-align: center;
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 50px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .overview-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 50px;
        align-items: start;
    }

    .overview-text h3 {
        font-size: 1.8rem;
        color: #1e3c72;
        margin-bottom: 20px;
    }

    .overview-text h4 {
        font-size: 1.3rem;
        color: #1e3c72;
        margin: 25px 0 15px 0;
    }

    .overview-text p {
        color: #666;
        line-height: 1.7;
        margin-bottom: 20px;
    }

    .highlight-list {
        list-style: none;
        padding-left: 0;
    }

    .highlight-list li {
        padding: 8px 0;
        color: #555;
        position: relative;
        padding-left: 25px;
    }

    .highlight-list li:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .overview-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .stat-item {
        text-align: center;
        padding: 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #ff6b35;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
    }

    .specialization-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .specialization-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: 1px solid #f0f0f0;
    }

    .specialization-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .spec-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        font-size: 1.8rem;
        color: white;
    }

    .specialization-card h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #1e3c72;
    }

    .specialization-card p {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .spec-features {
        list-style: none;
        padding-left: 0;
    }

    .spec-features li {
        padding: 5px 0;
        color: #555;
        position: relative;
        padding-left: 20px;
        font-size: 0.9rem;
    }

    .spec-features li:before {
        content: "•";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .outcomes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .outcome-item {
        text-align: center;
        padding: 20px;
    }

    .outcome-icon {
        width: 60px;
        height: 60px;
        background: #1e3c72;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        color: white;
        font-size: 1.5rem;
    }

    .outcome-item h4 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 10px;
        color: #1e3c72;
    }

    .outcome-item p {
        color: #666;
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .requirements-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        margin-top: 40px;
    }

    .req-column h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .req-list {
        list-style: none;
        padding-left: 0;
    }

    .req-list li {
        padding: 10px 0;
        color: #555;
        position: relative;
        padding-left: 25px;
        line-height: 1.5;
    }

    .req-list li:before {
        content: "→";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .cta-section {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
    }

    .cta-content {
        text-align: center;
        max-width: 600px;
        margin: 0 auto;
    }

    .cta-content h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: white;
    }

    .cta-content p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .cta-button-secondary {
        display: inline-block;
        background: transparent;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        border: 2px solid white;
        transition: all 0.3s ease;
    }

    .cta-button-secondary:hover {
        background: white;
        color: #1e3c72;
    }

    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .hero {
            padding: 60px 15px;
            min-height: 350px;
        }
        
        .hero h1 {
            font-size: 2.5rem;
        }
        
        .section {
            padding: 40px 0;
        }
        
        .section-title {
            font-size: 2rem;
        }
        
        .overview-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .overview-stats {
            grid-template-columns: 1fr 1fr;
        }
        
        .specialization-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .outcomes-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        
        .requirements-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .container {
            padding: 0 15px;
        }

        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }
    }

    @@media (max-width: 480px) {
        .hero h1 {
            font-size: 2rem;
        }
        
        .section-title {
            font-size: 1.8rem;
        }
        
        .overview-stats {
            grid-template-columns: 1fr;
        }
        
        .outcomes-grid {
            grid-template-columns: 1fr;
        }

        .cta-content h2 {
            font-size: 2rem;
        }
    }
</style>
