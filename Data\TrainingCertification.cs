using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CareerPortal.Data
{
    public class TrainingCertification
    {
        public int Id { get; set; }

        [Required]
        public int CandidateProfileId { get; set; }
        [ForeignKey("CandidateProfileId")]
        public CandidateProfile? CandidateProfile { get; set; }

        [Required]
        [StringLength(100)]
        public string? Name { get; set; }

        [Required]
        [StringLength(100)]
        public string? IssuingOrganization { get; set; }

        public DateTime? IssueDate { get; set; }

        public DateTime? ExpiryDate { get; set; } // Nullable if no expiry
        public DateTime CompletionDate { get; internal set; }
        public string CredentialId { get; internal set; }
        public string CredentialUrl { get; internal set; }
        public string Description { get; internal set; }
    }
}