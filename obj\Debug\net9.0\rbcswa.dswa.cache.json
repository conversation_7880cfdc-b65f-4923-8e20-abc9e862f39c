{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["1otk7ULXRG70g5CglU2PvvVeEwd93Z/iB/FCjKiufZ0=", "gQVU8i3PAQC2m8mFjOFQO82/lC6BczAjzJYmf+uwBGE=", "UZ9DNeje1OQjsSItcak9vIbSTe9sLxm2TpqE0at1xjg=", "Zkrz9BVNbjX+QVUOZmBOmOzHG68devCT147VhMHjpPo=", "HiYuzTf10ZN96yFWq7a/b8RKUq8IeQUnoWTiw+veeWo=", "CTfLkG9rM+flN8/tCuwe3pCEgQYDjAIwWtB5Q98uAYw=", "7KFiaegwcfCDn0UAPFEsRSaTDTF0PWf8s/VGbnpkii4=", "xiE1Gu78A0r6thfx+k1m79NF6Fx24a7u/e37F1r56Ik=", "zoHwqHN8K4YnxUt5Iuj3AffgwqrUyErBAaAzKOWcK0Q=", "ObhVLMIEgUDAUElAP3KDCP9OSgqa9xRQ+zOo/uEjuBU=", "1ZKWoU1LYDNMOvdgWIeKfYy3fvC5gR6d0tgYlyheZ8Y=", "LfLlRqQb8b5IRTFIERzqlRn55GXtvG6bsEtTCXPwlvk=", "Z/qm9Z0o93Q29Cf28EsOVW3O/2lWF7IHKARYx2qTvyQ=", "4k8P01pNnazh3qG7WQeEzCAr/OddcjzbwucOm5F+ZDs=", "4iZ/1TlFX3odY2EfodIMjObKli7GqNVE1kotdmW+j5I=", "jSg02Jc5UMuo4BtJ+Y5SnVzET1gKCBejQaa2TKR6kBk=", "CzpBP5yE/rEOsZnwvAINNMAVXLDzcntbwzAX46zAIDk=", "Wn/YhaL5A6us294YXy+WnDTfklMjbZsUMo+Y/26ekzc=", "XJO8Sq5G4XX6nDVXaqKaadbhih4V4PqgpEQiIHgj13I=", "4SBUN+vssExPlu4NvM1bWYj93oyOk++z0vzpEv8jKdE=", "EUSSDmnwaCegYfWPSv8eiMhc4v6two8Jj3L+BkcWarg=", "t/Erw4TXm3qlLrGMoZgy5Ddge8/o/JtRys5qlotpqPI=", "uu2QzskOFh39MD5v+XPz9z3oM3FmigspAln+GkY/edQ=", "gA3/gszdFplAfeNCYwZ2okkotDjlUUmkuUrYDJvPVqU=", "0vvlQnhtgBg4kqpQ3kLEmUmwJM7C+ZXDLEQQcH84XG8=", "A99t06uJ0qzjoX+EJedB6V4drefxpXDLVefv/m3wUk8=", "E5BxnLj3g4fGimuZiCaxAv3nsIyDRPRFArg9NCaXfoI=", "dJWVbzsY3RRmEjYCOcOYFdbVfS9AteFfIGx9VEcV7X4=", "KuFQauGnMxGh+cN2OBU89ycpcfBSAdiIHxP4rRIC19k=", "uoU+XU5UgCJ+tofccfP2BDBlb2RvTgImU8/qc+Juw/Q=", "vabzB3c8ZUfW2mILTiNd9Ev4OlqMvvj2MsEvsGpXFw0=", "d6YyCmRlgSPc3EJR34GQpbj/+5xmmVzk6VMSVdXd6LU=", "Ku9P/kRmCPTFvG8W1K2mgCLsmhmG/MjdjqmiVoTz66g=", "Y3EyQl2mdxETxJwMl0ouSEVhdFDho61uswow12Umpus=", "5REmi/QeZgskYyLqi1g1jlESgBCpESBIDTZZQ+oovs0=", "cOKiK/n98b0sFMSWpu2mQJml6F2tABsspYkLLY2i2uE=", "m+3nKxOPWbQKJ4Ilw8gNWzMU9bejDZA+Xm9FGXBaeAE=", "12teSscQG9bESvb4ye+3vKhBV+YsOwD2ZPmTBgxRhhc=", "EyyiDcFlng317C0aOHBuyOpWy/z60635faB72Tk7d14=", "ijQWgXllzY9QWKM+TW1zT+Zo1pAV3Er1jGBzMTGnc6s=", "/U6cioWJwEnxBQKBkwU2iXhQaQ+BKjMngRqFqmUDCAo="], "CachedAssets": {"1otk7ULXRG70g5CglU2PvvVeEwd93Z/iB/FCjKiufZ0=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-dpoev2zj9b.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Anchor/FluentAnchor.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fo9kun7a2u", "Integrity": "//clW2GxYKMKCQ11fp3GeXX3FIThT7MZFC8x35s69eU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "FileLength": 267, "LastWriteTime": "2025-05-31T12:28:16.3187951+00:00"}, "gQVU8i3PAQC2m8mFjOFQO82/lC6BczAjzJYmf+uwBGE=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-819w3ybe2d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "182vbtilql", "Integrity": "Afys6mc7pO9MdyokTQwtIDBaLTcYTTR9veWNwNIktvU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "FileLength": 1002, "LastWriteTime": "2025-05-31T12:28:16.322442+00:00"}, "UZ9DNeje1OQjsSItcak9vIbSTe9sLxm2TpqE0at1xjg=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-p6kf5zqzit.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Button/FluentButton.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cxryly5d9i", "Integrity": "ILEdH4KtR1Hb7JiJVs56TK2orcZyV4zmIUMwLCiQMaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "FileLength": 311, "LastWriteTime": "2025-05-31T12:28:16.3247993+00:00"}, "Zkrz9BVNbjX+QVUOZmBOmOzHG68devCT147VhMHjpPo=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-zjzit57lox.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0aw64dy5f3", "Integrity": "GN9Xra9rtwmLDCgEbgg9cmd8TqFT2hcKzoh25cmNDXU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "FileLength": 217, "LastWriteTime": "2025-05-31T12:28:16.3202965+00:00"}, "HiYuzTf10ZN96yFWq7a/b8RKUq8IeQUnoWTiw+veeWo=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-nfhyg6xvey.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7c22o04ryb", "Integrity": "qW8M83Rbn9fAXv7nGXmChE7Qo2htZhG8ALruzktPsU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "FileLength": 2962, "LastWriteTime": "2025-05-31T12:28:16.3237091+00:00"}, "CTfLkG9rM+flN8/tCuwe3pCEgQYDjAIwWtB5Q98uAYw=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-vyjqmndgy2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bo7nvuwvlw", "Integrity": "m/2KN+FpPqIvh9wR4E9l87AbfY4hA5AtyFeDpfKxDbc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "FileLength": 754, "LastWriteTime": "2025-05-31T12:28:16.3247993+00:00"}, "7KFiaegwcfCDn0UAPFEsRSaTDTF0PWf8s/VGbnpkii4=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-iy34mpf72d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Divider/FluentDivider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glesyzohpx", "Integrity": "5MY4SrjNix5pk9Q5gpyB70xS5vpChA2Ji/0LnyzJwjo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "FileLength": 231, "LastWriteTime": "2025-05-31T12:28:16.3263065+00:00"}, "xiE1Gu78A0r6thfx+k1m79NF6Fx24a7u/e37F1r56Ik=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-hi1gwvth64.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Grid/FluentGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9zocec3c57", "Integrity": "i0j3kxAp406ufS3MsxTNvjzVgyRqDwIajgOehqvtu6M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "FileLength": 759, "LastWriteTime": "2025-05-31T12:28:16.3397226+00:00"}, "zoHwqHN8K4YnxUt5Iuj3AffgwqrUyErBAaAzKOWcK0Q=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-5pcucyxosc.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o73xmx6c30", "Integrity": "hOhymz8Hwrl2LWr7Y0TXXQPcQmpfSCsFogOb6Zv2kB4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "FileLength": 138, "LastWriteTime": "2025-05-31T12:28:16.3506274+00:00"}, "ObhVLMIEgUDAUElAP3KDCP9OSgqa9xRQ+zOo/uEjuBU=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-vjluklws0l.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/InputFile/FluentInputFile.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1sdzkmuo8x", "Integrity": "gbwptaJwa99wlBJATqA39Wvd+hPAzyivnXV9kSwBbNk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "FileLength": 834, "LastWriteTime": "2025-05-31T12:28:16.3528873+00:00"}, "1ZKWoU1LYDNMOvdgWIeKfYy3fvC5gR6d0tgYlyheZ8Y=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-pu9hn1jugj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0dloqnx2v", "Integrity": "4VrmdaAeF/CVMJ7a55YiF2hEI0LDZi84KcPQcvi3ZM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "FileLength": 891, "LastWriteTime": "2025-05-31T12:28:16.3247993+00:00"}, "LfLlRqQb8b5IRTFIERzqlRn55GXtvG6bsEtTCXPwlvk=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-xp2f0e0rh3.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Label/FluentInputLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nmysnspdoa", "Integrity": "T9UdQlDeqtgxKoaOrC15cRB5b8Xw+a6l3AS6Y+XsXL8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "FileLength": 251, "LastWriteTime": "2025-05-31T12:28:16.3263065+00:00"}, "Z/qm9Z0o93Q29Cf28EsOVW3O/2lWF7IHKARYx2qTvyQ=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-psptt994gq.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentAutocomplete.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b9j8p69emz", "Integrity": "gUtYatED8B/J0nKbZybxmXWnL/UJrKEPtENtEKPigwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "FileLength": 570, "LastWriteTime": "2025-05-31T12:28:16.3397226+00:00"}, "4k8P01pNnazh3qG7WQeEzCAr/OddcjzbwucOm5F+ZDs=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-afevzs963z.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentCombobox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "inru90os05", "Integrity": "fhAJBRWOqIUuZBy3gnCpMXRnHWRrtx32KmxhrDCzqLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "FileLength": 544, "LastWriteTime": "2025-05-31T12:28:16.3273494+00:00"}, "4iZ/1TlFX3odY2EfodIMjObKli7GqNVE1kotdmW+j5I=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-mmp1yy7un5.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/ListComponentBase.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1s7y80tos", "Integrity": "uEs3SkTKioIAKwT/E2rYx8lRyA3csV0YWEB5VboYALI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "FileLength": 137, "LastWriteTime": "2025-05-31T12:28:16.3284073+00:00"}, "jSg02Jc5UMuo4BtJ+Y5SnVzET1gKCBejQaa2TKR6kBk=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-5wrroj4j54.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Menu/FluentMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7qvv8cqi0", "Integrity": "aXVPltYYB1IqzLOOo/ihlIm1QLjjfas+mNS4YtuShCI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "FileLength": 298, "LastWriteTime": "2025-05-31T12:28:16.3407368+00:00"}, "CzpBP5yE/rEOsZnwvAINNMAVXLDzcntbwzAX46zAIDk=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-9fmja7pljs.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ch14addp29", "Integrity": "gI4r2CiVSDsh0E4W8EzzQkWVMP1z5KqWsHc5UHSf4lA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "FileLength": 1012, "LastWriteTime": "2025-05-31T12:28:16.3273494+00:00"}, "Wn/YhaL5A6us294YXy+WnDTfklMjbZsUMo+Y/26ekzc=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-rgycuwl3sw.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overflow/FluentOverflow.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "88o5cg3t4w", "Integrity": "GLrvqsihiaOKSTpR4HUdwLbyE5ovO/PgdDHABPeQqQ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "FileLength": 1755, "LastWriteTime": "2025-05-31T12:28:16.3296602+00:00"}, "XJO8Sq5G4XX6nDVXaqKaadbhih4V4PqgpEQiIHgj13I=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-kjm33rwg1a.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overlay/FluentOverlay.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s2s2rdqeh5", "Integrity": "XJbbN4OuwYXj7k13JHIv9Lj1t118BSzODet/C6GywE0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "FileLength": 656, "LastWriteTime": "2025-05-31T12:28:16.3407368+00:00"}, "4SBUN+vssExPlu4NvM1bWYj93oyOk++z0vzpEv8jKdE=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-awzanx0pu8.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m5zicgu0uv", "Integrity": "ei4JgX2LTuVjcaPCISvNeX1hHKeJ8uYfuz0+kGtJAkI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "FileLength": 1844, "LastWriteTime": "2025-05-31T12:28:16.3516275+00:00"}, "EUSSDmnwaCegYfWPSv8eiMhc4v6two8Jj3L+BkcWarg=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\uslingtyva-m0sdc2vg34.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Search/FluentSearch.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w98asdg00m", "Integrity": "ZDiIU0h55N8SSpI62bLHZqaEzTK3XYMZ7oWs+4kRUmo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "FileLength": 327, "LastWriteTime": "2025-05-31T12:28:16.3420903+00:00"}, "t/Erw4TXm3qlLrGMoZgy5Ddge8/o/JtRys5qlotpqPI=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-0b0bj86z40.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSlider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azl6ax9okv", "Integrity": "Ok3+UlpFQuzZXWBv0rF9abeEFcHU9G9/yGNlsDrEjXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "FileLength": 284, "LastWriteTime": "2025-05-31T12:28:16.3420903+00:00"}, "uu2QzskOFh39MD5v+XPz9z3oM3FmigspAln+GkY/edQ=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-e5lgg05xwp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSliderLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k78ptnt0os", "Integrity": "MEEF9qZp8vzu++12GS1Nrm9HsAwBidvWQdUYcJ+WJvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "FileLength": 219, "LastWriteTime": "2025-05-31T12:28:16.3435581+00:00"}, "gA3/gszdFplAfeNCYwZ2okkotDjlUUmkuUrYDJvPVqU=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-ki10xp5gks.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/SortableList/FluentSortableList.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tkabjbg0r", "Integrity": "2E4hhc6jOpgtINxrSRMKIqw+DQLQ2SftfcX1ThOrepM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "FileLength": 469, "LastWriteTime": "2025-05-31T12:28:16.3460672+00:00"}, "0vvlQnhtgBg4kqpQ3kLEmUmwJM7C+ZXDLEQQcH84XG8=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-s9hcthfn4x.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mxbb0zqj51", "Integrity": "1iL0Rcjo40DSIIzfew0csjsBypwBYzFxskd2cyvll1c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "FileLength": 1349, "LastWriteTime": "2025-05-31T12:28:16.3496298+00:00"}, "A99t06uJ0qzjoX+EJedB6V4drefxpXDLVefv/m3wUk8=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-idf8r2y2gj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tabs/FluentTab.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7vrwdrxykv", "Integrity": "7aHGbSJcSaT5cfjQK+Gyrtoyd3MYNywpo1mPbW862yk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "FileLength": 293, "LastWriteTime": "2025-05-31T12:28:16.3247993+00:00"}, "E5BxnLj3g4fGimuZiCaxAv3nsIyDRPRFArg9NCaXfoI=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-btwuipzwbp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/TextField/FluentTextField.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ov9jz4ar3", "Integrity": "mCekEzA0zB8lJvQIv6M/2Ndz99hlmD0UE8rEDHgUAps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "FileLength": 464, "LastWriteTime": "2025-05-31T12:28:16.3284073+00:00"}, "dJWVbzsY3RRmEjYCOcOYFdbVfS9AteFfIGx9VEcV7X4=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-v95crb0bvb.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Toolbar/FluentToolbar.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4ao9gsby09", "Integrity": "1OzW47M+BAzhQZXA66lSbRUQZP1ObnRRM4TTFKNDCh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "FileLength": 513, "LastWriteTime": "2025-05-31T12:28:16.3407368+00:00"}, "KuFQauGnMxGh+cN2OBU89ycpcfBSAdiIHxP4rRIC19k=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\is0nail857-b0dyrub9as.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tooltip/FluentTooltip.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fg0nienog3", "Integrity": "8ePQzkTFcBOjLSt8PxALnbVMRW1l7AfeMHcGCtC1Jdg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "FileLength": 276, "LastWriteTime": "2025-05-31T12:28:16.3425605+00:00"}, "uoU+XU5UgCJ+tofccfP2BDBlb2RvTgImU8/qc+Juw/Q=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-1dlotxxwer.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "css/reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2cbdkxfmct", "Integrity": "Xl2r/uacHVD+mrIVAVDC5GAc5mMeiGjzQRAeyrzLwzc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "FileLength": 2181, "LastWriteTime": "2025-05-31T12:28:16.3445599+00:00"}, "vabzB3c8ZUfW2mILTiNd9Ev4OlqMvvj2MsEvsGpXFw0=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-f8c5bd5212.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/initializersLoader.webview.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mnaqae6vjn", "Integrity": "juVE/OQUgf6veO9EE5R7w8sUbTnBaMtqSwEftLHbeM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "FileLength": 513, "LastWriteTime": "2025-05-31T12:28:16.3284073+00:00"}, "d6YyCmRlgSPc3EJR34GQpbj/+5xmmVzk6VMSVdXd6LU=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-t5s4sbrbsi.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/loading-theme.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rgr9t677zw", "Integrity": "SzheGEdps0ArWi83idGsg6m6WlDMfdv4S5PpD4i+7pE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "FileLength": 1182, "LastWriteTime": "2025-05-31T12:28:16.3425605+00:00"}, "Ku9P/kRmCPTFvG8W1K2mgCLsmhmG/MjdjqmiVoTz66g=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-y92cxfqtgl.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3a8h9hbomh", "Integrity": "1X6i8A29INvrhpx2iST6R2pGeSEC9jwBm/t4w6I3TyA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "FileLength": 90259, "LastWriteTime": "2025-05-31T12:28:16.3664536+00:00"}, "Y3EyQl2mdxETxJwMl0ouSEVhdFDho61uswow12Umpus=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-kz8gc8cxma.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t8zskhp41d", "Integrity": "k1bvyfht4MPa+2CGIYyjCqpmHAmcIb02LT50Ty0KClk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "FileLength": 568, "LastWriteTime": "2025-05-31T12:28:16.3445599+00:00"}, "5REmi/QeZgskYyLqi1g1jlESgBCpESBIDTZZQ+oovs0=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-8j22j5h3b2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dj93cmo8cl", "Integrity": "94gUrH5jYenrkpJAKcu8Rcc6G3T/PvRHDTVVsp6p+lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "FileLength": 278620, "LastWriteTime": "2025-05-31T12:28:16.4214946+00:00"}, "cOKiK/n98b0sFMSWpu2mQJml6F2tABsspYkLLY2i2uE=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "app#[.{fingerprint=kwazt7t2v0}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aan7m9jccg", "Integrity": "ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "FileLength": 2332, "LastWriteTime": "2025-05-31T12:28:16.3296602+00:00"}, "m+3nKxOPWbQKJ4Ilw8gNWzMU9bejDZA+Xm9FGXBaeAE=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-ow8g543z8n.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "css/landing#[.{fingerprint=ow8g543z8n}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h0j2wsp0lr", "Integrity": "P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "FileLength": 2152, "LastWriteTime": "2025-05-31T12:28:16.3407368+00:00"}, "EyyiDcFlng317C0aOHBuyOpWy/z60635faB72Tk7d14=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2hecizl59g-uhfllo7vmv.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal.modules.json.gz", "AssetKind": "Build", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pmmudzy78n", "Integrity": "EUcPo74MSmhv/hLMcB1hGxjI+dVgHeDs4j3s9WycJ0A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "FileLength": 93, "LastWriteTime": "2025-05-31T12:28:16.3435581+00:00"}, "ijQWgXllzY9QWKM+TW1zT+Zo1pAV3Er1jGBzMTGnc6s=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dr5gsymsw-q21vm7bk8w.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v99o1hjjup", "Integrity": "DUYRo3aBXcG5TcO2iCbSCYEify4c77QCT7R4VobttpI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "FileLength": 14001, "LastWriteTime": "2025-05-31T12:28:16.3539081+00:00"}, "/U6cioWJwEnxBQKBkwU2iXhQaQ+BKjMngRqFqmUDCAo=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal#[.{fingerprint=tnv30r1bl8}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5p5exba8qm", "Integrity": "p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "FileLength": 109, "LastWriteTime": "2025-05-31T12:28:16.3187951+00:00"}, "12teSscQG9bESvb4ye+3vKhBV+YsOwD2ZPmTBgxRhhc=": {"Identity": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "favicon#[.{fingerprint=a8m5cweeeb}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7r53oxhn9o", "Integrity": "40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "FileLength": 5357, "LastWriteTime": "2025-05-31T12:28:16.3445599+00:00"}}, "CachedCopyCandidates": {}}