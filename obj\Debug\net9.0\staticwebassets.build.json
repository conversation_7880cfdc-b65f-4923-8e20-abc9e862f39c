{"Version": 1, "Hash": "2Y/itTKMYP1Dyr9FogJ4MzQRyFwdcbhOExWhV2boYfQ=", "Source": "CareerPort<PERSON>", "BasePath": "_content/CareerPortal", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "CareerPortal\\wwwroot", "Source": "CareerPort<PERSON>", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\", "BasePath": "_content/CareerPortal", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Anchor/FluentAnchor.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dpoev2zj9b", "Integrity": "b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "FileLength": 430, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "819w3ybe2d", "Integrity": "RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "FileLength": 3215, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Button/FluentButton.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "p6kf5zqzit", "Integrity": "8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "FileLength": 522, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Checkbox/FluentCheckbox.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zjzit57lox", "Integrity": "gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "FileLength": 368, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DataGrid/FluentDataGrid.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nfhyg6xvey", "Integrity": "PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "FileLength": 14173, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DesignSystemProvider/FluentDesignTheme.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vyjqmndgy2", "Integrity": "abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "FileLength": 2058, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Divider/FluentDivider.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iy34mpf72d", "Integrity": "CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "FileLength": 388, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Grid/FluentGrid.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hi1gwvth64", "Integrity": "V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "FileLength": 3005, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5pcucyxosc", "Integrity": "yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "FileLength": 348, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/InputFile/FluentInputFile.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vjluklws0l", "Integrity": "m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "FileLength": 2813, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/KeyCode/FluentKeyCode.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pu9hn1jugj", "Integrity": "3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "FileLength": 3477, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Label/FluentInputLabel.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xp2f0e0rh3", "Integrity": "hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "FileLength": 473, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentAutocomplete.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "psptt994gq", "Integrity": "2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "FileLength": 1299, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentCombobox.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "afevzs963z", "Integrity": "OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "FileLength": 1483, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/ListComponentBase.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mmp1yy7un5", "Integrity": "/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "FileLength": 177, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Menu/FluentMenu.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5wrroj4j54", "Integrity": "C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "FileLength": 725, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/NavMenu/FluentNavMenu.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9fmja7pljs", "Integrity": "u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "FileLength": 5345, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overflow/FluentOverflow.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rgycuwl3sw", "Integrity": "hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "FileLength": 6575, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overlay/FluentOverlay.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kjm33rwg1a", "Integrity": "IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "FileLength": 1977, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/PullToRefresh/FluentPullToRefresh.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "awzanx0pu8", "Integrity": "xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "FileLength": 6841, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Search/FluentSearch.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m0sdc2vg34", "Integrity": "FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "FileLength": 917, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSlider.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0b0bj86z40", "Integrity": "TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "FileLength": 445, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSliderLabel.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e5lgg05xwp", "Integrity": "Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "FileLength": 340, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/SortableList/FluentSortableList.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ki10xp5gks", "Integrity": "rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "FileLength": 1325, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Splitter/FluentMultiSplitter.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s9hcthfn4x", "Integrity": "kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "FileLength": 6140, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tabs/FluentTab.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "idf8r2y2gj", "Integrity": "Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "FileLength": 526, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/TextField/FluentTextField.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "btwuipzwbp", "Integrity": "YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "FileLength": 1187, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Toolbar/FluentToolbar.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v95crb0bvb", "Integrity": "s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "FileLength": 1364, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tooltip/FluentTooltip.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b0dyrub9as", "Integrity": "pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "FileLength": 730, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "css/reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1dlotxxwer", "Integrity": "2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "FileLength": 7992, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/initializersLoader.webview.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "f8c5bd5212", "Integrity": "L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "FileLength": 1121, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/loading-theme.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t5s4sbrbsi", "Integrity": "kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "FileLength": 3190, "LastWriteTime": "2025-02-18T12:58:02+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "JSModule", "AssetTraitValue": "JSLibraryModule", "Fingerprint": "y92cxfqtgl", "Integrity": "htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "FileLength": 392863, "LastWriteTime": "2025-02-18T13:05:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kz8gc8cxma", "Integrity": "gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "FileLength": 1022, "LastWriteTime": "2025-02-18T13:05:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8j22j5h3b2", "Integrity": "VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "FileLength": 1313081, "LastWriteTime": "2025-02-18T13:05:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "q21vm7bk8w", "Integrity": "zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "FileLength": 101053, "LastWriteTime": "2025-02-18T13:05:28+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-0b0bj86z40.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSlider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azl6ax9okv", "Integrity": "Ok3+UlpFQuzZXWBv0rF9abeEFcHU9G9/yGNlsDrEjXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "FileLength": 284, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2hecizl59g-uhfllo7vmv.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal.modules.json.gz", "AssetKind": "Build", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pmmudzy78n", "Integrity": "EUcPo74MSmhv/hLMcB1hGxjI+dVgHeDs4j3s9WycJ0A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "FileLength": 93, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "favicon#[.{fingerprint=a8m5cweeeb}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7r53oxhn9o", "Integrity": "40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "FileLength": 5357, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-8j22j5h3b2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dj93cmo8cl", "Integrity": "94gUrH5jYenrkpJAKcu8Rcc6G3T/PvRHDTVVsp6p+lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "FileLength": 278620, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-vyjqmndgy2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bo7nvuwvlw", "Integrity": "m/2KN+FpPqIvh9wR4E9l87AbfY4hA5AtyFeDpfKxDbc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "FileLength": 754, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-9fmja7pljs.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ch14addp29", "Integrity": "gI4r2CiVSDsh0E4W8EzzQkWVMP1z5KqWsHc5UHSf4lA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "FileLength": 1012, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-s9hcthfn4x.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mxbb0zqj51", "Integrity": "1iL0Rcjo40DSIIzfew0csjsBypwBYzFxskd2cyvll1c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "FileLength": 1349, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dr5gsymsw-q21vm7bk8w.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v99o1hjjup", "Integrity": "DUYRo3aBXcG5TcO2iCbSCYEify4c77QCT7R4VobttpI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "FileLength": 14001, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-e5lgg05xwp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSliderLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k78ptnt0os", "Integrity": "MEEF9qZp8vzu++12GS1Nrm9HsAwBidvWQdUYcJ+WJvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "FileLength": 219, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-hi1gwvth64.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Grid/FluentGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9zocec3c57", "Integrity": "i0j3kxAp406ufS3MsxTNvjzVgyRqDwIajgOehqvtu6M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "FileLength": 759, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-1dlotxxwer.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "css/reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2cbdkxfmct", "Integrity": "Xl2r/uacHVD+mrIVAVDC5GAc5mMeiGjzQRAeyrzLwzc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "FileLength": 2181, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-psptt994gq.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentAutocomplete.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b9j8p69emz", "Integrity": "gUtYatED8B/J0nKbZybxmXWnL/UJrKEPtENtEKPigwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "FileLength": 570, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-v95crb0bvb.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Toolbar/FluentToolbar.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4ao9gsby09", "Integrity": "1OzW47M+BAzhQZXA66lSbRUQZP1ObnRRM4TTFKNDCh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "FileLength": 513, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-iy34mpf72d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Divider/FluentDivider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glesyzohpx", "Integrity": "5MY4SrjNix5pk9Q5gpyB70xS5vpChA2Ji/0LnyzJwjo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "FileLength": 231, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-ow8g543z8n.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "css/landing#[.{fingerprint=ow8g543z8n}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h0j2wsp0lr", "Integrity": "P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "FileLength": 2152, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal#[.{fingerprint=tnv30r1bl8}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5p5exba8qm", "Integrity": "p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "FileLength": 109, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-p6kf5zqzit.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Button/FluentButton.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cxryly5d9i", "Integrity": "ILEdH4KtR1Hb7JiJVs56TK2orcZyV4zmIUMwLCiQMaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "FileLength": 311, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-dpoev2zj9b.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Anchor/FluentAnchor.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fo9kun7a2u", "Integrity": "//clW2GxYKMKCQ11fp3GeXX3FIThT7MZFC8x35s69eU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "FileLength": 267, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-zjzit57lox.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0aw64dy5f3", "Integrity": "GN9Xra9rtwmLDCgEbgg9cmd8TqFT2hcKzoh25cmNDXU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "FileLength": 217, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-mmp1yy7un5.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/ListComponentBase.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1s7y80tos", "Integrity": "uEs3SkTKioIAKwT/E2rYx8lRyA3csV0YWEB5VboYALI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "FileLength": 137, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-ki10xp5gks.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/SortableList/FluentSortableList.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tkabjbg0r", "Integrity": "2E4hhc6jOpgtINxrSRMKIqw+DQLQ2SftfcX1ThOrepM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "FileLength": 469, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-vjluklws0l.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/InputFile/FluentInputFile.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1sdzkmuo8x", "Integrity": "gbwptaJwa99wlBJATqA39Wvd+hPAzyivnXV9kSwBbNk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "FileLength": 834, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CareerPortal", "RelativePath": "app#[.{fingerprint=kwazt7t2v0}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aan7m9jccg", "Integrity": "ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "FileLength": 2332, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-xp2f0e0rh3.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Label/FluentInputLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nmysnspdoa", "Integrity": "T9UdQlDeqtgxKoaOrC15cRB5b8Xw+a6l3AS6Y+XsXL8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "FileLength": 251, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\is0nail857-b0dyrub9as.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tooltip/FluentTooltip.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fg0nienog3", "Integrity": "8ePQzkTFcBOjLSt8PxALnbVMRW1l7AfeMHcGCtC1Jdg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "FileLength": 276, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-awzanx0pu8.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m5zicgu0uv", "Integrity": "ei4JgX2LTuVjcaPCISvNeX1hHKeJ8uYfuz0+kGtJAkI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "FileLength": 1844, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-nfhyg6xvey.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7c22o04ryb", "Integrity": "qW8M83Rbn9fAXv7nGXmChE7Qo2htZhG8ALruzktPsU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "FileLength": 2962, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-y92cxfqtgl.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3a8h9hbomh", "Integrity": "1X6i8A29INvrhpx2iST6R2pGeSEC9jwBm/t4w6I3TyA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "FileLength": 90259, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-f8c5bd5212.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/initializersLoader.webview.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mnaqae6vjn", "Integrity": "juVE/OQUgf6veO9EE5R7w8sUbTnBaMtqSwEftLHbeM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "FileLength": 513, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-kz8gc8cxma.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t8zskhp41d", "Integrity": "k1bvyfht4MPa+2CGIYyjCqpmHAmcIb02LT50Ty0KClk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "FileLength": 568, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-rgycuwl3sw.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overflow/FluentOverflow.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "88o5cg3t4w", "Integrity": "GLrvqsihiaOKSTpR4HUdwLbyE5ovO/PgdDHABPeQqQ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "FileLength": 1755, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-5pcucyxosc.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o73xmx6c30", "Integrity": "hOhymz8Hwrl2LWr7Y0TXXQPcQmpfSCsFogOb6Zv2kB4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "FileLength": 138, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-5wrroj4j54.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Menu/FluentMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7qvv8cqi0", "Integrity": "aXVPltYYB1IqzLOOo/ihlIm1QLjjfas+mNS4YtuShCI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "FileLength": 298, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-t5s4sbrbsi.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/loading-theme.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rgr9t677zw", "Integrity": "SzheGEdps0ArWi83idGsg6m6WlDMfdv4S5PpD4i+7pE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "FileLength": 1182, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-btwuipzwbp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/TextField/FluentTextField.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ov9jz4ar3", "Integrity": "mCekEzA0zB8lJvQIv6M/2Ndz99hlmD0UE8rEDHgUAps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "FileLength": 464, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-idf8r2y2gj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tabs/FluentTab.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7vrwdrxykv", "Integrity": "7aHGbSJcSaT5cfjQK+Gyrtoyd3MYNywpo1mPbW862yk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "FileLength": 293, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-afevzs963z.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentCombobox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "inru90os05", "Integrity": "fhAJBRWOqIUuZBy3gnCpMXRnHWRrtx32KmxhrDCzqLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "FileLength": 544, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-819w3ybe2d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "182vbtilql", "Integrity": "Afys6mc7pO9MdyokTQwtIDBaLTcYTTR9veWNwNIktvU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "FileLength": 1002, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\uslingtyva-m0sdc2vg34.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Search/FluentSearch.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w98asdg00m", "Integrity": "ZDiIU0h55N8SSpI62bLHZqaEzTK3XYMZ7oWs+4kRUmo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "FileLength": 327, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-pu9hn1jugj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0dloqnx2v", "Integrity": "4VrmdaAeF/CVMJ7a55YiF2hEI0LDZi84KcPQcvi3ZM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "FileLength": 891, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-kjm33rwg1a.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overlay/FluentOverlay.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s2s2rdqeh5", "Integrity": "XJbbN4OuwYXj7k13JHIv9Lj1t118BSzODet/C6GywE0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "FileLength": 656, "LastWriteTime": "2025-05-31T11:21:17+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal.modules.json", "AssetKind": "Build", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "JSModule", "AssetTraitValue": "JSModuleManifest", "Fingerprint": "uhfllo7vmv", "Integrity": "sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "FileLength": 114, "LastWriteTime": "2025-05-31T11:15:44+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "SourceId": "CareerPort<PERSON>", "SourceType": "Computed", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\", "BasePath": "_content/CareerPortal", "RelativePath": "CareerPortal#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "tnv30r1bl8", "Integrity": "Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "FileLength": 131, "LastWriteTime": "2025-05-31T11:15:44+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\", "BasePath": "_content/CareerPortal", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kwazt7t2v0", "Integrity": "DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 5583, "LastWriteTime": "2025-05-31T05:34:33+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\", "BasePath": "_content/CareerPortal", "RelativePath": "css/landing#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ow8g543z8n", "Integrity": "GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\landing.css", "FileLength": 9772, "LastWriteTime": "2025-05-31T10:01:22+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\", "BasePath": "_content/CareerPortal", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "a8m5cweeeb", "Integrity": "ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 15086, "LastWriteTime": "2025-05-31T05:34:33+00:00"}, {"Identity": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\images\\logo.png", "SourceId": "CareerPort<PERSON>", "SourceType": "Discovered", "ContentRoot": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\", "BasePath": "_content/CareerPortal", "RelativePath": "images/logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6sw81bt7n3", "Integrity": "CmtfNBcJ/NLjuatoucTNotcZb3PLhf4ZU7tPG2bsqco=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\logo.png", "FileLength": 3808, "LastWriteTime": "2025-01-31T18:09:49+00:00"}], "Endpoints": [{"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-dpoev2zj9b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003731343284"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267"}, {"Name": "ETag", "Value": "\"//clW2GxYKMKCQ11fp3GeXX3FIThT7MZFC8x35s69eU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-dpoev2zj9b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"//clW2GxYKMKCQ11fp3GeXX3FIThT7MZFC8x35s69eU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-//clW2GxYKMKCQ11fp3GeXX3FIThT7MZFC8x35s69eU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-819w3ybe2d.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000997008973"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1002"}, {"Name": "ETag", "Value": "\"Afys6mc7pO9MdyokTQwtIDBaLTcYTTR9veWNwNIktvU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-819w3ybe2d.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1002"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Afys6mc7pO9MdyokTQwtIDBaLTcYTTR9veWNwNIktvU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Afys6mc7pO9MdyokTQwtIDBaLTcYTTR9veWNwNIktvU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "522"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-p6kf5zqzit.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003205128205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "311"}, {"Name": "ETag", "Value": "\"ILEdH4KtR1Hb7JiJVs56TK2orcZyV4zmIUMwLCiQMaE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-p6kf5zqzit.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "311"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ILEdH4KtR1Hb7JiJVs56TK2orcZyV4zmIUMwLCiQMaE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ILEdH4KtR1Hb7JiJVs56TK2orcZyV4zmIUMwLCiQMaE="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "368"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-zjzit57lox.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004587155963"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "217"}, {"Name": "ETag", "Value": "\"GN9Xra9rtwmLDCgEbgg9cmd8TqFT2hcKzoh25cmNDXU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-zjzit57lox.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "217"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GN9Xra9rtwmLDCgEbgg9cmd8TqFT2hcKzoh25cmNDXU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GN9Xra9rtwmLDCgEbgg9cmd8TqFT2hcKzoh25cmNDXU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14173"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-nfhyg6xvey.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000337495781"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2962"}, {"Name": "ETag", "Value": "\"qW8M83Rbn9fAXv7nGXmChE7Qo2htZhG8ALruzktPsU8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-nfhyg6xvey.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2962"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qW8M83Rbn9fAXv7nGXmChE7Qo2htZhG8ALruzktPsU8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qW8M83Rbn9fAXv7nGXmChE7Qo2htZhG8ALruzktPsU8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2058"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-vyjqmndgy2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001324503311"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "754"}, {"Name": "ETag", "Value": "\"m/2KN+FpPqIvh9wR4E9l87AbfY4hA5AtyFeDpfKxDbc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-vyjqmndgy2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "754"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m/2KN+FpPqIvh9wR4E9l87AbfY4hA5AtyFeDpfKxDbc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m/2KN+FpPqIvh9wR4E9l87AbfY4hA5AtyFeDpfKxDbc="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "388"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-iy34mpf72d.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004310344828"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "ETag", "Value": "\"5MY4SrjNix5pk9Q5gpyB70xS5vpChA2Ji/0LnyzJwjo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-iy34mpf72d.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5MY4SrjNix5pk9Q5gpyB70xS5vpChA2Ji/0LnyzJwjo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5MY4SrjNix5pk9Q5gpyB70xS5vpChA2Ji/0LnyzJwjo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3005"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-hi1gwvth64.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001315789474"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "759"}, {"Name": "ETag", "Value": "\"i0j3kxAp406ufS3MsxTNvjzVgyRqDwIajgOehqvtu6M=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-hi1gwvth64.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "759"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"i0j3kxAp406ufS3MsxTNvjzVgyRqDwIajgOehqvtu6M=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-i0j3kxAp406ufS3MsxTNvjzVgyRqDwIajgOehqvtu6M="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "348"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-5pcucyxosc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007194244604"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "138"}, {"Name": "ETag", "Value": "\"hOhymz8Hwrl2LWr7Y0TXXQPcQmpfSCsFogOb6Zv2kB4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-5pcucyxosc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "138"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hOhymz8Hwrl2LWr7Y0TXXQPcQmpfSCsFogOb6Zv2kB4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hOhymz8Hwrl2LWr7Y0TXXQPcQmpfSCsFogOb6Zv2kB4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2813"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-vjluklws0l.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001197604790"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "ETag", "Value": "\"gbwptaJwa99wlBJATqA39Wvd+hPAzyivnXV9kSwBbNk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-vjluklws0l.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gbwptaJwa99wlBJATqA39Wvd+hPAzyivnXV9kSwBbNk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gbwptaJwa99wlBJATqA39Wvd+hPAzyivnXV9kSwBbNk="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-pu9hn1jugj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001121076233"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "891"}, {"Name": "ETag", "Value": "\"4VrmdaAeF/CVMJ7a55YiF2hEI0LDZi84KcPQcvi3ZM4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-pu9hn1jugj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "891"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4VrmdaAeF/CVMJ7a55YiF2hEI0LDZi84KcPQcvi3ZM4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4VrmdaAeF/CVMJ7a55YiF2hEI0LDZi84KcPQcvi3ZM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "473"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-xp2f0e0rh3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003968253968"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "251"}, {"Name": "ETag", "Value": "\"T9UdQlDeqtgxKoaOrC15cRB5b8Xw+a6l3AS6Y+XsXL8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-xp2f0e0rh3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "251"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T9UdQlDeqtgxKoaOrC15cRB5b8Xw+a6l3AS6Y+XsXL8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T9UdQlDeqtgxKoaOrC15cRB5b8Xw+a6l3AS6Y+XsXL8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1299"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-psptt994gq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001751313485"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "570"}, {"Name": "ETag", "Value": "\"gUtYatED8B/J0nKbZybxmXWnL/UJrKEPtENtEKPigwg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-psptt994gq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "570"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gUtYatED8B/J0nKbZybxmXWnL/UJrKEPtENtEKPigwg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gUtYatED8B/J0nKbZybxmXWnL/UJrKEPtENtEKPigwg="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1483"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-afevzs963z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001834862385"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "544"}, {"Name": "ETag", "Value": "\"fhAJBRWOqIUuZBy3gnCpMXRnHWRrtx32KmxhrDCzqLQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-afevzs963z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "544"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fhAJBRWOqIUuZBy3gnCpMXRnHWRrtx32KmxhrDCzqLQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fhAJBRWOqIUuZBy3gnCpMXRnHWRrtx32KmxhrDCzqLQ="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-mmp1yy7un5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007246376812"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "137"}, {"Name": "ETag", "Value": "\"uEs3SkTKioIAKwT/E2rYx8lRyA3csV0YWEB5VboYALI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-mmp1yy7un5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "137"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uEs3SkTKioIAKwT/E2rYx8lRyA3csV0YWEB5VboYALI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uEs3SkTKioIAKwT/E2rYx8lRyA3csV0YWEB5VboYALI="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "725"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-5wrroj4j54.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003344481605"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "298"}, {"Name": "ETag", "Value": "\"aXVPltYYB1IqzLOOo/ihlIm1QLjjfas+mNS4YtuShCI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-5wrroj4j54.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aXVPltYYB1IqzLOOo/ihlIm1QLjjfas+mNS4YtuShCI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aXVPltYYB1IqzLOOo/ihlIm1QLjjfas+mNS4YtuShCI="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5345"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-9fmja7pljs.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000987166831"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1012"}, {"Name": "ETag", "Value": "\"gI4r2CiVSDsh0E4W8EzzQkWVMP1z5KqWsHc5UHSf4lA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-9fmja7pljs.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1012"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gI4r2CiVSDsh0E4W8EzzQkWVMP1z5KqWsHc5UHSf4lA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gI4r2CiVSDsh0E4W8EzzQkWVMP1z5KqWsHc5UHSf4lA="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6575"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-rgycuwl3sw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000569476082"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1755"}, {"Name": "ETag", "Value": "\"GLrvqsihiaOKSTpR4HUdwLbyE5ovO/PgdDHABPeQqQ4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-rgycuwl3sw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1755"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GLrvqsihiaOKSTpR4HUdwLbyE5ovO/PgdDHABPeQqQ4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GLrvqsihiaOKSTpR4HUdwLbyE5ovO/PgdDHABPeQqQ4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-kjm33rwg1a.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001522070015"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "656"}, {"Name": "ETag", "Value": "\"XJbbN4OuwYXj7k13JHIv9Lj1t118BSzODet/C6GywE0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-kjm33rwg1a.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "656"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XJbbN4OuwYXj7k13JHIv9Lj1t118BSzODet/C6GywE0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XJbbN4OuwYXj7k13JHIv9Lj1t118BSzODet/C6GywE0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6841"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-awzanx0pu8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000542005420"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1844"}, {"Name": "ETag", "Value": "\"ei4JgX2LTuVjcaPCISvNeX1hHKeJ8uYfuz0+kGtJAkI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-awzanx0pu8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1844"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ei4JgX2LTuVjcaPCISvNeX1hHKeJ8uYfuz0+kGtJAkI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ei4JgX2LTuVjcaPCISvNeX1hHKeJ8uYfuz0+kGtJAkI="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "917"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\uslingtyva-m0sdc2vg34.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003048780488"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "327"}, {"Name": "ETag", "Value": "\"ZDiIU0h55N8SSpI62bLHZqaEzTK3XYMZ7oWs+4kRUmo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\uslingtyva-m0sdc2vg34.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "327"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZDiIU0h55N8SSpI62bLHZqaEzTK3XYMZ7oWs+4kRUmo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZDiIU0h55N8SSpI62bLHZqaEzTK3XYMZ7oWs+4kRUmo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "445"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-0b0bj86z40.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003508771930"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "284"}, {"Name": "ETag", "Value": "\"Ok3+UlpFQuzZXWBv0rF9abeEFcHU9G9/yGNlsDrEjXw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-0b0bj86z40.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ok3+UlpFQuzZXWBv0rF9abeEFcHU9G9/yGNlsDrEjXw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ok3+UlpFQuzZXWBv0rF9abeEFcHU9G9/yGNlsDrEjXw="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-e5lgg05xwp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004545454545"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "219"}, {"Name": "ETag", "Value": "\"MEEF9qZp8vzu++12GS1Nrm9HsAwBidvWQdUYcJ+WJvY=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-e5lgg05xwp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MEEF9qZp8vzu++12GS1Nrm9HsAwBidvWQdUYcJ+WJvY=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MEEF9qZp8vzu++12GS1Nrm9HsAwBidvWQdUYcJ+WJvY="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1325"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-ki10xp5gks.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002127659574"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "469"}, {"Name": "ETag", "Value": "\"2E4hhc6jOpgtINxrSRMKIqw+DQLQ2SftfcX1ThOrepM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-ki10xp5gks.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "469"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2E4hhc6jOpgtINxrSRMKIqw+DQLQ2SftfcX1ThOrepM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2E4hhc6jOpgtINxrSRMKIqw+DQLQ2SftfcX1ThOrepM="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6140"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-s9hcthfn4x.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740740741"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1349"}, {"Name": "ETag", "Value": "\"1iL0Rcjo40DSIIzfew0csjsBypwBYzFxskd2cyvll1c=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-s9hcthfn4x.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1349"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1iL0Rcjo40DSIIzfew0csjsBypwBYzFxskd2cyvll1c=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1iL0Rcjo40DSIIzfew0csjsBypwBYzFxskd2cyvll1c="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "526"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-idf8r2y2gj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003401360544"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "293"}, {"Name": "ETag", "Value": "\"7aHGbSJcSaT5cfjQK+Gyrtoyd3MYNywpo1mPbW862yk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-idf8r2y2gj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "293"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7aHGbSJcSaT5cfjQK+Gyrtoyd3MYNywpo1mPbW862yk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7aHGbSJcSaT5cfjQK+Gyrtoyd3MYNywpo1mPbW862yk="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1187"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-btwuipzwbp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002150537634"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "ETag", "Value": "\"mCekEzA0zB8lJvQIv6M/2Ndz99hlmD0UE8rEDHgUAps=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-btwuipzwbp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mCekEzA0zB8lJvQIv6M/2Ndz99hlmD0UE8rEDHgUAps=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mCekEzA0zB8lJvQIv6M/2Ndz99hlmD0UE8rEDHgUAps="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1364"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-v95crb0bvb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001945525292"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "513"}, {"Name": "ETag", "Value": "\"1OzW47M+BAzhQZXA66lSbRUQZP1ObnRRM4TTFKNDCh4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-v95crb0bvb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "513"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1OzW47M+BAzhQZXA66lSbRUQZP1ObnRRM4TTFKNDCh4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1OzW47M+BAzhQZXA66lSbRUQZP1ObnRRM4TTFKNDCh4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "730"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\is0nail857-b0dyrub9as.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003610108303"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "276"}, {"Name": "ETag", "Value": "\"8ePQzkTFcBOjLSt8PxALnbVMRW1l7AfeMHcGCtC1Jdg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\is0nail857-b0dyrub9as.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8ePQzkTFcBOjLSt8PxALnbVMRW1l7AfeMHcGCtC1Jdg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8ePQzkTFcBOjLSt8PxALnbVMRW1l7AfeMHcGCtC1Jdg="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.1dlotxxwer.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7992"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1dlotxxwer"}, {"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7992"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-1dlotxxwer.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000458295142"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2181"}, {"Name": "ETag", "Value": "\"Xl2r/uacHVD+mrIVAVDC5GAc5mMeiGjzQRAeyrzLwzc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-1dlotxxwer.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2181"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Xl2r/uacHVD+mrIVAVDC5GAc5mMeiGjzQRAeyrzLwzc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xl2r/uacHVD+mrIVAVDC5GAc5mMeiGjzQRAeyrzLwzc="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.f8c5bd5212.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f8c5bd5212"}, {"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-f8c5bd5212.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001945525292"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "513"}, {"Name": "ETag", "Value": "\"juVE/OQUgf6veO9EE5R7w8sUbTnBaMtqSwEftLHbeM4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-f8c5bd5212.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "513"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"juVE/OQUgf6veO9EE5R7w8sUbTnBaMtqSwEftLHbeM4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-juVE/OQUgf6veO9EE5R7w8sUbTnBaMtqSwEftLHbeM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3190"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-t5s4sbrbsi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000845308538"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1182"}, {"Name": "ETag", "Value": "\"SzheGEdps0ArWi83idGsg6m6WlDMfdv4S5PpD4i+7pE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-t5s4sbrbsi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1182"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SzheGEdps0ArWi83idGsg6m6WlDMfdv4S5PpD4i+7pE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SzheGEdps0ArWi83idGsg6m6WlDMfdv4S5PpD4i+7pE="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.t5s4sbrbsi.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3190"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t5s4sbrbsi"}, {"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "101053"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dr5gsymsw-q21vm7bk8w.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071418369"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14001"}, {"Name": "ETag", "Value": "\"DUYRo3aBXcG5TcO2iCbSCYEify4c77QCT7R4VobttpI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "392863"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "dependency-group", "Value": "js-initializer"}, {"Name": "integrity", "Value": "sha256-htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-y92cxfqtgl.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011079105"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "90259"}, {"Name": "ETag", "Value": "\"1X6i8A29INvrhpx2iST6R2pGeSEC9jwBm/t4w6I3TyA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM=\""}], "EndpointProperties": [{"Name": "dependency-group", "Value": "js-initializer"}, {"Name": "integrity", "Value": "sha256-htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-y92cxfqtgl.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "90259"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1X6i8A29INvrhpx2iST6R2pGeSEC9jwBm/t4w6I3TyA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1X6i8A29INvrhpx2iST6R2pGeSEC9jwBm/t4w6I3TyA="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1022"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-kz8gc8cxma.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001757469244"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "568"}, {"Name": "ETag", "Value": "\"k1bvyfht4MPa+2CGIYyjCqpmHAmcIb02LT50Ty0KClk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-kz8gc8cxma.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"k1bvyfht4MPa+2CGIYyjCqpmHAmcIb02LT50Ty0KClk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k1bvyfht4MPa+2CGIYyjCqpmHAmcIb02LT50Ty0KClk="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1313081"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-8j22j5h3b2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000003589105"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "278620"}, {"Name": "ETag", "Value": "\"94gUrH5jYenrkpJAKcu8Rcc6G3T/PvRHDTVVsp6p+lo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-8j22j5h3b2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "278620"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"94gUrH5jYenrkpJAKcu8Rcc6G3T/PvRHDTVVsp6p+lo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-94gUrH5jYenrkpJAKcu8Rcc6G3T/PvRHDTVVsp6p+lo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "101053"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q21vm7bk8w"}, {"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\5dr5gsymsw-q21vm7bk8w.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14001"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DUYRo3aBXcG5TcO2iCbSCYEify4c77QCT7R4VobttpI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DUYRo3aBXcG5TcO2iCbSCYEify4c77QCT7R4VobttpI="}]}, {"Route": "app.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000428632662"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2332"}, {"Name": "ETag", "Value": "\"ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5583"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.css.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2332"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE="}]}, {"Route": "app.kwazt7t2v0.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000428632662"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2332"}, {"Name": "ETag", "Value": "\"ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.kwazt7t2v0.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5583"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.kwazt7t2v0.css.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\hpxq4k1yf6-kwazt7t2v0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2332"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "label", "Value": "app.css.gz"}, {"Name": "integrity", "Value": "sha256-ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE="}]}, {"Route": "CareerPortal.modules.json", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2hecizl59g-uhfllo7vmv.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "ETag", "Value": "\"EUcPo74MSmhv/hLMcB1hGxjI+dVgHeDs4j3s9WycJ0A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "W/\"sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig="}]}, {"Route": "CareerPortal.modules.json", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:15:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig="}]}, {"Route": "CareerPortal.modules.json.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2hecizl59g-uhfllo7vmv.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"EUcPo74MSmhv/hLMcB1hGxjI+dVgHeDs4j3s9WycJ0A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EUcPo74MSmhv/hLMcB1hGxjI+dVgHeDs4j3s9WycJ0A="}]}, {"Route": "CareerPortal.styles.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.009090909091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "109"}, {"Name": "ETag", "Value": "\"p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}]}, {"Route": "CareerPortal.styles.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:15:44 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}]}, {"Route": "CareerPortal.styles.css.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM="}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.009090909091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "109"}, {"Name": "ETag", "Value": "\"p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}, {"Name": "label", "Value": "CareerPortal.styles.css"}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CareerPortal.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:15:44 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}, {"Name": "label", "Value": "CareerPortal.styles.css"}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\9w4jwsdqh9-tnv30r1bl8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "label", "Value": "CareerPortal.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM="}]}, {"Route": "css/landing.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-ow8g543z8n.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000464468184"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2152"}, {"Name": "ETag", "Value": "\"P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc="}]}, {"Route": "css/landing.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9772"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 10:01:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc="}]}, {"Route": "css/landing.css.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-ow8g543z8n.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2152"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI="}]}, {"Route": "css/landing.ow8g543z8n.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-ow8g543z8n.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000464468184"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2152"}, {"Name": "ETag", "Value": "\"P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ow8g543z8n"}, {"Name": "label", "Value": "css/landing.css"}, {"Name": "integrity", "Value": "sha256-GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc="}]}, {"Route": "css/landing.ow8g543z8n.css", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\css\\landing.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9772"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 10:01:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ow8g543z8n"}, {"Name": "label", "Value": "css/landing.css"}, {"Name": "integrity", "Value": "sha256-GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc="}]}, {"Route": "css/landing.ow8g543z8n.css.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\8nrps8m2up-ow8g543z8n.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2152"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ow8g543z8n"}, {"Name": "label", "Value": "css/landing.css.gz"}, {"Name": "integrity", "Value": "sha256-P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI="}]}, {"Route": "favicon.a8m5cweeeb.ico", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000186636805"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5357"}, {"Name": "ETag", "Value": "\"40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.a8m5cweeeb.ico", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.a8m5cweeeb.ico.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5357"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "label", "Value": "favicon.ico.gz"}, {"Name": "integrity", "Value": "sha256-40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI="}]}, {"Route": "favicon.ico", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000186636805"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5357"}, {"Name": "ETag", "Value": "\"40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.ico", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.ico.gz", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\Debug\\net9.0\\compressed\\2qd731de3k-a8m5cweeeb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5357"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI="}]}, {"Route": "images/logo.6sw81bt7n3.png", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\images\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3808"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CmtfNBcJ/NLjuatoucTNotcZb3PLhf4ZU7tPG2bsqco=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 31 Jan 2025 18:09:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6sw81bt7n3"}, {"Name": "label", "Value": "images/logo.png"}, {"Name": "integrity", "Value": "sha256-CmtfNBcJ/NLjuatoucTNotcZb3PLhf4ZU7tPG2bsqco="}]}, {"Route": "images/logo.png", "AssetFile": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\wwwroot\\images\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3808"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CmtfNBcJ/NLjuatoucTNotcZb3PLhf4ZU7tPG2bsqco=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 31 Jan 2025 18:09:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CmtfNBcJ/NLjuatoucTNotcZb3PLhf4ZU7tPG2bsqco="}]}]}