using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CareerPortal.Data
{
    public class Education
    {
        public int Id { get; set; }

        [Required]
        public int CandidateProfileId { get; set; }
        [ForeignKey("CandidateProfileId")]
        public CandidateProfile? CandidateProfile { get; set; }

        [Required]
        [StringLength(100)]
        public string? DegreeName { get; set; }

        [Required]
        [StringLength(100)]
        public string? InstitutionName { get; set; }

        public int? GraduationYear { get; set; }

        [StringLength(10)]
        public string? GradeOrGPA { get; set; }
        public string? Degree { get; internal set; }
        public string? Institution { get; internal set; }
        public string FieldOfStudy { get; internal set; }
        public DateTime StartDate { get; internal set; }
        public DateTime? EndDate { get; internal set; }
        public bool IsCurrentlyStudying { get; internal set; }
        public decimal? GPA { get; internal set; }
        public string Description { get; internal set; }
    }
}