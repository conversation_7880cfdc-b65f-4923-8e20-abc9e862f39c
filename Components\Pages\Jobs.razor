@page "/jobs"
@layout CareerPortal.Components.Layout.CareerPortalLayout
@using CareerPortal.Data
@using Microsoft.EntityFrameworkCore
@inject ApplicationDbContext DbContext
@inject NavigationManager Navigation

<PageTitle>Job Opportunities - Career Portal</PageTitle>

<div class="jobs-page">
    <div class="jobs-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Current Job Opportunities</h1>
            <p class="page-subtitle">Discover exciting career opportunities and join our team</p>
        </div>

        <!-- Search and Filter Section -->
        <div class="search-section">
            <div class="search-container">
                <div class="search-box">
                    <input type="text" @bind="searchTerm" @oninput="OnSearchInput" 
                           placeholder="Search jobs by title, department, or location..." 
                           class="search-input" />
                    <button class="search-button" @onclick="SearchJobs">
                        🔍
                    </button>
                </div>
                
                <div class="filter-row">
                    <select @bind="selectedDepartment" @bind:after="FilterJobs" class="filter-select">
                        <option value="">All Departments</option>
                        @foreach (var dept in departments)
                        {
                            <option value="@dept">@dept</option>
                        }
                    </select>
                    
                    <select @bind="selectedLocation" @bind:after="FilterJobs" class="filter-select">
                        <option value="">All Locations</option>
                        @foreach (var loc in locations)
                        {
                            <option value="@loc">@loc</option>
                        }
                    </select>
                    
                    <button class="clear-filters-button" @onclick="ClearFilters">
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Jobs Grid -->
        <div class="jobs-grid">
            @if (filteredJobs?.Any() == true)
            {
                @foreach (var job in filteredJobs)
                {
                    <div class="job-card" @onclick="() => ViewJobDetails(job.Id)">
                        <div class="job-header">
                            <h3 class="job-title">@job.Title</h3>
                            <span class="job-department">@job.Department</span>
                        </div>
                        
                        <div class="job-meta">
                            <div class="job-location">
                                <span class="meta-icon">📍</span>
                                @job.Location
                            </div>
                            <div class="job-posted">
                                <span class="meta-icon">📅</span>
                                Posted @GetRelativeTime(job.PostedDate)
                            </div>
                        </div>
                        
                        <div class="job-description">
                            @GetJobSummary(job.Description)
                        </div>
                        
                        <div class="job-footer">
                            @if (job.ClosingDate.HasValue)
                            {
                                <div class="closing-date">
                                    <span class="meta-icon">⏰</span>
                                    Closes: @job.ClosingDate.Value.ToString("MMM dd, yyyy")
                                </div>
                            }
                            <button class="view-details-button">
                                View Details →
                            </button>
                        </div>
                    </div>
                }
            }
            else if (isLoading)
            {
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>Loading job opportunities...</p>
                </div>
            }
            else
            {
                <div class="no-jobs-state">
                    <div class="no-jobs-icon">💼</div>
                    <h3>No jobs found</h3>
                    <p>Try adjusting your search criteria or check back later for new opportunities.</p>
                </div>
            }
        </div>

        <!-- Results Summary -->
        @if (filteredJobs?.Any() == true)
        {
            <div class="results-summary">
                Showing @filteredJobs.Count() of @allJobs.Count() job opportunities
            </div>
        }
    </div>
</div>

<style>
    .jobs-page {
        background-color: #f8f9fa;
        min-height: calc(100vh - 70px);
        padding: 40px 0;
    }

    .jobs-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .page-header {
        text-align: center;
        margin-bottom: 40px;
    }

    .page-title {
        font-size: 2.5rem;
        color: #1e3c72;
        margin-bottom: 10px;
        font-weight: 700;
    }

    .page-subtitle {
        font-size: 1.1rem;
        color: #666;
        max-width: 600px;
        margin: 0 auto;
    }

    .search-section {
        background: white;
        border-radius: 12px;
        padding: 30px;
        margin-bottom: 40px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .search-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .search-box {
        display: flex;
        margin-bottom: 20px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .search-input {
        flex: 1;
        padding: 15px 20px;
        border: none;
        font-size: 1rem;
        outline: none;
    }

    .search-button {
        background-color: #1e3c72;
        color: white;
        border: none;
        padding: 15px 25px;
        cursor: pointer;
        font-size: 1.2rem;
        transition: background-color 0.3s ease;
    }

    .search-button:hover {
        background-color: #2a5298;
    }

    .filter-row {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
    }

    .filter-select {
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 0.95rem;
        background: white;
        min-width: 150px;
    }

    .clear-filters-button {
        background: none;
        border: 1px solid #dc3545;
        color: #dc3545;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .clear-filters-button:hover {
        background-color: #dc3545;
        color: white;
    }

    .jobs-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    .job-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid transparent;
    }

    .job-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        border-color: #1e3c72;
    }

    .job-header {
        margin-bottom: 15px;
    }

    .job-title {
        font-size: 1.3rem;
        color: #1e3c72;
        margin-bottom: 8px;
        font-weight: 600;
    }

    .job-department {
        background-color: #e3f2fd;
        color: #1e3c72;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .job-meta {
        display: flex;
        gap: 20px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }

    .job-location,
    .job-posted {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #666;
        font-size: 0.9rem;
    }

    .meta-icon {
        font-size: 0.9rem;
    }

    .job-description {
        color: #555;
        line-height: 1.6;
        margin-bottom: 20px;
        font-size: 0.95rem;
    }

    .job-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .closing-date {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #dc3545;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .view-details-button {
        background-color: #1e3c72;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 500;
        transition: background-color 0.3s ease;
    }

    .view-details-button:hover {
        background-color: #2a5298;
    }

    .loading-state,
    .no-jobs-state {
        grid-column: 1 / -1;
        text-align: center;
        padding: 60px 20px;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #1e3c72;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .no-jobs-icon {
        font-size: 4rem;
        margin-bottom: 20px;
    }

    .no-jobs-state h3 {
        color: #1e3c72;
        margin-bottom: 10px;
    }

    .no-jobs-state p {
        color: #666;
        max-width: 400px;
        margin: 0 auto;
    }

    .results-summary {
        text-align: center;
        color: #666;
        font-size: 0.95rem;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .jobs-container {
            padding: 0 15px;
        }

        .page-title {
            font-size: 2rem;
        }

        .search-section {
            padding: 20px;
        }

        .jobs-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .job-card {
            padding: 20px;
        }

        .filter-row {
            flex-direction: column;
            align-items: stretch;
        }

        .filter-select {
            min-width: auto;
        }
    }

    @@media (max-width: 480px) {
        .jobs-page {
            padding: 20px 0;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .search-section {
            margin-bottom: 30px;
        }

        .job-meta {
            flex-direction: column;
            gap: 8px;
        }

        .job-footer {
            flex-direction: column;
            align-items: stretch;
            gap: 15px;
        }
    }
</style>

@code {
    private List<Vacancy> allJobs = new();
    private IEnumerable<Vacancy> filteredJobs = new List<Vacancy>();
    private List<string> departments = new();
    private List<string> locations = new();
    
    private string searchTerm = "";
    private string selectedDepartment = "";
    private string selectedLocation = "";
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadJobs();
        isLoading = false;
    }

    private async Task LoadJobs()
    {
        allJobs = await DbContext.Vacancies
            .Where(v => v.IsActive)
            .OrderByDescending(v => v.PostedDate)
            .ToListAsync();

        departments = allJobs.Select(j => j.Department).Distinct().OrderBy(d => d).ToList();
        locations = allJobs.Select(j => j.Location).Distinct().OrderBy(l => l).ToList();
        
        filteredJobs = allJobs;
    }

    private void OnSearchInput(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? "";
        FilterJobs();
    }

    private void SearchJobs()
    {
        FilterJobs();
    }

    private void FilterJobs()
    {
        filteredJobs = allJobs.Where(job =>
            (string.IsNullOrEmpty(searchTerm) || 
             job.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
             job.Department.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
             job.Location.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
             job.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) &&
            (string.IsNullOrEmpty(selectedDepartment) || job.Department == selectedDepartment) &&
            (string.IsNullOrEmpty(selectedLocation) || job.Location == selectedLocation)
        );
    }

    private void ClearFilters()
    {
        searchTerm = "";
        selectedDepartment = "";
        selectedLocation = "";
        filteredJobs = allJobs;
    }

    private void ViewJobDetails(int jobId)
    {
        Navigation.NavigateTo($"/job/{jobId}");
    }

    private string GetRelativeTime(DateTime postedDate)
    {
        var timeSpan = DateTime.UtcNow - postedDate;
        
        if (timeSpan.Days > 0)
            return $"{timeSpan.Days} day{(timeSpan.Days > 1 ? "s" : "")} ago";
        else if (timeSpan.Hours > 0)
            return $"{timeSpan.Hours} hour{(timeSpan.Hours > 1 ? "s" : "")} ago";
        else
            return "Recently";
    }

    private string GetJobSummary(string description)
    {
        if (string.IsNullOrEmpty(description))
            return "";
            
        var words = description.Split(' ');
        if (words.Length <= 25)
            return description;
            
        return string.Join(" ", words.Take(25)) + "...";
    }
}
