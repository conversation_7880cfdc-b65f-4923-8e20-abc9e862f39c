{"format": 1, "restore": {"e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\CareerPortal.csproj": {}}, "projects": {"e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\CareerPortal.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\CareerPortal.csproj", "projectName": "CareerPort<PERSON>", "projectPath": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\CareerPortal.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\rnd\\demo3\\CareerPortal\\CareerPortal\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AspNet.Security.OAuth.LinkedIn": {"target": "Package", "version": "[9.4.0, )"}, "Microsoft.AspNetCore.Authentication.Google": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.AspNetCore.Authentication.MicrosoftAccount": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.FluentUI.AspNetCore.Components": {"target": "Package", "version": "[4.11.5, )"}, "Microsoft.FluentUI.AspNetCore.Components.Icons": {"target": "Package", "version": "[4.11.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}