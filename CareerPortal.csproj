<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-CareerPortal-de13a469-1c7b-46b9-822f-c0dafcf9e69a</UserSecretsId>
    <EnableStaticWebAssetsCompression>false</EnableStaticWebAssetsCompression>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNet.Security.OAuth.LinkedIn" Version="9.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.11.5" />
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.11.5" />
  </ItemGroup>
</Project>
