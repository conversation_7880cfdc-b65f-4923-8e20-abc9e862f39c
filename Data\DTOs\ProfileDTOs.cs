using System.ComponentModel.DataAnnotations;

namespace CareerPortal.Data.DTOs
{
    public class PersonalInfoDto
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = "";

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = "";

        [Phone]
        [StringLength(20)]
        public string? MobileNumber { get; set; }

        [Phone]
        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        [StringLength(255)]
        public string? Address { get; set; }

        [StringLength(20)]
        public string? CNIC { get; set; }

        [StringLength(50)]
        public string? PassportNumber { get; set; }

        [Url]
        [StringLength(255)]
        public string? LinkedInProfileUrl { get; set; }

        public string? ProfileSummary { get; set; }
    }

    public class ExperienceDto
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string JobTitle { get; set; } = "";

        [Required]
        [StringLength(100)]
        public string CompanyName { get; set; } = "";

        [StringLength(50)]
        public string? Location { get; set; }

        [Required]
        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public bool IsCurrentJob { get; set; }

        public string? JobDescription { get; set; }

        public string? KeyAchievements { get; set; }
    }

    public class EducationDto
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Degree { get; set; } = "";

        [Required]
        [StringLength(100)]
        public string Institution { get; set; } = "";

        [StringLength(100)]
        public string? FieldOfStudy { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public bool IsCurrentlyStudying { get; set; }

        [Range(0, 4.0)]
        public decimal? GPA { get; set; }

        public string? Description { get; set; }
    }

    public class TrainingCertificationDto
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = "";

        [Required]
        [StringLength(100)]
        public string IssuingOrganization { get; set; } = "";

        [Required]
        public DateTime CompletionDate { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(100)]
        public string? CredentialId { get; set; }

        [Url]
        [StringLength(255)]
        public string? CredentialUrl { get; set; }

        public string? Description { get; set; }
    }

    public class SkillDto
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Name { get; set; } = "";

        [StringLength(50)]
        public string? Category { get; set; }

        [Range(1, 5)]
        public int? ProficiencyLevel { get; set; } = 1;

        public bool IsVerified { get; set; }
    }

    public class AchievementAwardDto
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Title { get; set; } = "";

        [StringLength(100)]
        public string? IssuingOrganization { get; set; }

        [Required]
        public DateTime DateReceived { get; set; }

        public string? Description { get; set; }
    }

    public class JobAlertPreferenceDto
    {
        public int Id { get; set; }

        [StringLength(100)]
        public string? Keywords { get; set; }

        [StringLength(50)]
        public string? LocationPreference { get; set; }

        [StringLength(50)]
        public string? JobTypePreference { get; set; }

        [StringLength(50)]
        public string? DepartmentPreference { get; set; }

        [Range(0, 200000)]
        public decimal? MinSalary { get; set; }

        [Range(0, 500000)]
        public decimal? MaxSalary { get; set; }

        public bool EmailNotifications { get; set; } = true;

        public bool IsActive { get; set; } = true;
    }

    public class CompleteProfileDto
    {
        public PersonalInfoDto PersonalInfo { get; set; } = new();
        public List<ExperienceDto> Experiences { get; set; } = new();
        public List<EducationDto> Educations { get; set; } = new();
        public List<TrainingCertificationDto> TrainingCertifications { get; set; } = new();
        public List<SkillDto> Skills { get; set; } = new();
        public List<AchievementAwardDto> AchievementAwards { get; set; } = new();
        public List<JobAlertPreferenceDto> JobAlertPreferences { get; set; } = new();
    }
}
