@inherits LayoutComponentBase

<div class="page-layout">
    <!-- Global Navigation -->
    <GlobalNavigation />

    <!-- Main Content -->
    <main class="main-content">
        @Body
    </main>

    <!-- Footer -->
    <footer class="global-footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="/images/logo.png" alt="Company Logo" class="footer-logo-image" />
                    </div>
                    <p class="footer-description">
                        Pakistan's leading automotive company driving excellence in mobility solutions.
                    </p>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="/">Home</a></li>
                        <li><a href="/jobs">Careers</a></li>
                        <li><a href="/about">About Us</a></li>
                        <li><a href="/contact">Contact</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">For Candidates</h4>
                    <ul class="footer-links">
                        <li><a href="/Account/Register">Create Account</a></li>
                        <li><a href="/profile">Manage Profile</a></li>
                        <li><a href="/job-preferences">Job Alerts</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title">Contact Info</h4>
                    <div class="contact-info">
                        <p>📧 <EMAIL></p>
                        <p>📞 +92-21-***********</p>
                        <p>📍 Karachi, Pakistan</p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <p>&copy; @DateTime.Now.Year Pak Suzuki Motor Company Limited. All rights reserved.</p>
                    <div class="footer-bottom-links">
                        <a href="/privacy">Privacy Policy</a>
                        <a href="/terms">Terms of Service</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>

<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f8f9fa;
    }

    .page-layout {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .main-content {
        flex: 1;
        padding: 0;
    }

    .global-footer {
        background-color: #1e3c72;
        color: white;
        margin-top: auto;
    }

    .footer-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .footer-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 40px;
        padding: 50px 0 30px;
    }

    .footer-section h4.footer-title {
        color: #fff;
        margin-bottom: 20px;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .footer-logo-image {
        height: 60px;
        width: auto;
        margin-bottom: 15px;
        filter: brightness(0) invert(1);
    }

    .footer-description {
        color: #b8c5d6;
        line-height: 1.6;
        margin-bottom: 20px;
    }

    .footer-links {
        list-style: none;
    }

    .footer-links li {
        margin-bottom: 10px;
    }

    .footer-links a {
        color: #b8c5d6;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .footer-links a:hover {
        color: #fff;
    }

    .contact-info p {
        color: #b8c5d6;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .footer-bottom {
        border-top: 1px solid #2a5298;
        padding: 20px 0;
    }

    .footer-bottom-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;
    }

    .footer-bottom p {
        color: #b8c5d6;
        font-size: 0.9rem;
    }

    .footer-bottom-links {
        display: flex;
        gap: 20px;
    }

    .footer-bottom-links a {
        color: #b8c5d6;
        text-decoration: none;
        font-size: 0.9rem;
        transition: color 0.3s ease;
    }

    .footer-bottom-links a:hover {
        color: #fff;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .footer-content {
            grid-template-columns: 1fr;
            gap: 30px;
            padding: 40px 0 20px;
        }

        .footer-container {
            padding: 0 15px;
        }

        .footer-bottom-content {
            flex-direction: column;
            text-align: center;
            gap: 15px;
        }

        .footer-bottom-links {
            justify-content: center;
        }
    }

    @@media (max-width: 480px) {
        .footer-content {
            gap: 25px;
            padding: 30px 0 15px;
        }

        .footer-logo-image {
            height: 50px;
        }

        .footer-bottom-links {
            flex-direction: column;
            gap: 10px;
        }
    }

    /* Blazor Error UI */
    #blazor-error-ui {
        background: lightyellow;
        bottom: 0;
        box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
        display: none;
        left: 0;
        padding: 0.6rem 1.25rem 0.7rem 1.25rem;
        position: fixed;
        width: 100%;
        z-index: 1000;
    }

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

    .blazor-error-boundary {
        background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHZpZXdCb3g9IjAgMCA1NiA0OSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjUgNi41TDIwLjUgMTIuNUwyNy41IDYuNSIgc3Ryb2tlPSIjMUMyNEU5IiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNMTMuNSA0Mi41TDIwLjUgMzYuNUwyNy41IDQyLjUiIHN0cm9rZT0iIzFDMjRFOSIgc3Ryb2tlLXdpZHRoPSIzIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+) no-repeat 1rem/1.8rem, #b32121;
        padding: 1rem 1rem 1rem 3.7rem;
        color: white;
    }

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }
</style>
