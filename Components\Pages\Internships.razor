@page "/internships"
@layout CareerPortal.Components.Layout.CareerPortalLayout

<PageTitle>Internship Program - Pak Suzuki Career Portal</PageTitle>

<!-- Hero Section -->
<section class="hero">
    <div class="hero-content">
        <h1>Internship Program</h1>
        <p>Launch your career with hands-on experience at Pakistan's leading automotive company. Our comprehensive internship program offers real-world exposure and professional development opportunities.</p>
        <a href="#programs" class="cta-button">Explore Programs</a>
    </div>
</section>

<!-- Program Overview Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Why Choose Our Internship Program?</h2>
        <p class="section-subtitle">Experience the automotive industry from the inside with mentorship, training, and meaningful projects</p>

        <div class="features-grid">
            <div class="feature-item">
                <div class="feature-icon">🎯</div>
                <h4>Real Projects</h4>
                <p>Work on actual business challenges and contribute to meaningful projects that impact our operations.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">👥</div>
                <h4>Expert Mentorship</h4>
                <p>Learn from experienced professionals who will guide your development and career growth.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">📚</div>
                <h4>Skill Development</h4>
                <p>Participate in training sessions, workshops, and skill-building activities tailored to your field.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">🌟</div>
                <h4>Career Opportunities</h4>
                <p>Outstanding interns may receive full-time job offers upon graduation.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">🤝</div>
                <h4>Networking</h4>
                <p>Build professional relationships and expand your network within the automotive industry.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">💰</div>
                <h4>Competitive Stipend</h4>
                <p>Receive a competitive monthly stipend along with other benefits during your internship.</p>
            </div>
        </div>
    </div>
</section>

<!-- Internship Programs Section -->
<section class="section" style="background-color: #f8f9fa;" id="programs">
    <div class="container">
        <h2 class="section-title">Internship Programs</h2>
        <p class="section-subtitle">Choose from our specialized internship tracks designed for different academic backgrounds</p>

        <div class="program-grid">
            <div class="program-card">
                <div class="program-icon">🎓</div>
                <h3>Engineering Internship</h3>
                <p>Perfect for engineering students looking to gain hands-on experience in automotive design, manufacturing, quality assurance, and R&D. Work with cutting-edge technology and innovative projects.</p>
                <ul class="program-features">
                    <li>Automotive Design & Development</li>
                    <li>Manufacturing Processes</li>
                    <li>Quality Control & Testing</li>
                    <li>Research & Development</li>
                </ul>
                <a href="/internships/engineering" class="learn-more">Learn More →</a>
            </div>

            <div class="program-card">
                <div class="program-icon">💼</div>
                <h3>Business Internship</h3>
                <p>Ideal for business, management, and commerce students. Gain exposure to various business functions including sales, marketing, finance, operations, and strategic planning.</p>
                <ul class="program-features">
                    <li>Sales & Marketing</li>
                    <li>Financial Analysis</li>
                    <li>Operations Management</li>
                    <li>Strategic Planning</li>
                </ul>
                <a href="/internships/business" class="learn-more">Learn More →</a>
            </div>

            <div class="program-card">
                <div class="program-icon">🔧</div>
                <h3>Technical Internship</h3>
                <p>Designed for technical students interested in automotive technology, maintenance, IT systems, and technical support services. Get hands-on experience with modern automotive systems.</p>
                <ul class="program-features">
                    <li>Automotive Technology</li>
                    <li>IT Systems & Software</li>
                    <li>Technical Support</li>
                    <li>Maintenance & Service</li>
                </ul>
                <a href="/internships/technical" class="learn-more">Learn More →</a>
            </div>
        </div>
    </div>
</section>

<!-- Application Process Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Application Process</h2>
        <p class="section-subtitle">Follow these simple steps to apply for our internship program</p>

        <div class="process-steps">
            <div class="step">
                <div class="step-number">1</div>
                <h4>Online Application</h4>
                <p>Submit your application through our online portal with your resume and academic transcripts.</p>
            </div>

            <div class="step">
                <div class="step-number">2</div>
                <h4>Initial Screening</h4>
                <p>Our HR team will review your application and contact qualified candidates for the next step.</p>
            </div>

            <div class="step">
                <div class="step-number">3</div>
                <h4>Assessment & Interview</h4>
                <p>Participate in our assessment process and interview with department managers.</p>
            </div>

            <div class="step">
                <div class="step-number">4</div>
                <h4>Final Selection</h4>
                <p>Successful candidates will receive an offer letter with internship details and start date.</p>
            </div>
        </div>
    </div>
</section>

<!-- Requirements Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Eligibility Requirements</h2>
        
        <div class="requirements-grid">
            <div class="requirement-card">
                <h4>Academic Requirements</h4>
                <ul>
                    <li>Currently enrolled in a recognized university</li>
                    <li>Minimum CGPA of 3.0 or equivalent</li>
                    <li>Relevant field of study for chosen program</li>
                    <li>Available for 8-12 weeks internship</li>
                </ul>
            </div>

            <div class="requirement-card">
                <h4>Skills & Attributes</h4>
                <ul>
                    <li>Strong communication skills</li>
                    <li>Eagerness to learn and adapt</li>
                    <li>Team collaboration abilities</li>
                    <li>Problem-solving mindset</li>
                </ul>
            </div>

            <div class="requirement-card">
                <h4>Documents Required</h4>
                <ul>
                    <li>Updated resume/CV</li>
                    <li>Academic transcripts</li>
                    <li>Cover letter</li>
                    <li>University recommendation letter</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section cta-section">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Start Your Journey?</h2>
            <p>Join our internship program and take the first step towards a successful career in the automotive industry.</p>
            <div class="cta-buttons">
                <a href="/Account/Register" class="cta-button">Apply Now</a>
                <a href="/jobs" class="cta-button-secondary">View All Opportunities</a>
            </div>
        </div>
    </div>
</section>

<style>
    /* Use the same styles from landing.css */
    .landing-page * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .hero {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 80px 20px;
        text-align: center;
        position: relative;
        min-height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 800px;
    }

    .hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        color: white;
    }

    .hero p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-button {
        display: inline-block;
        background: #ff6b35;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    }

    .cta-button:hover {
        background: #e55a2b;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        color: white;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section {
        padding: 60px 0;
        width: 100%;
    }

    .section-title {
        text-align: center;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .section-subtitle {
        text-align: center;
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 50px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .feature-item {
        text-align: center;
        padding: 20px;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: #ff6b35;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        color: white;
        font-size: 1.5rem;
    }

    .feature-item h4 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 10px;
        color: #1e3c72;
    }

    .feature-item p {
        color: #666;
        font-size: 0.95rem;
    }

    .program-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .program-card {
        background: white;
        border-radius: 15px;
        padding: 40px 30px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: 1px solid #f0f0f0;
    }

    .program-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .program-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 25px;
        font-size: 2rem;
        color: white;
    }

    .program-card h3 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #1e3c72;
    }

    .program-card p {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .program-features {
        list-style: none;
        text-align: left;
        margin-bottom: 25px;
    }

    .program-features li {
        padding: 5px 0;
        color: #555;
        position: relative;
        padding-left: 20px;
    }

    .program-features li:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .learn-more {
        color: #1e3c72;
        text-decoration: none;
        font-weight: 600;
        border-bottom: 2px solid transparent;
        transition: border-color 0.3s ease;
    }

    .learn-more:hover {
        border-bottom-color: #1e3c72;
        color: #1e3c72;
    }

    .process-steps {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .step {
        text-align: center;
        padding: 20px;
    }

    .step-number {
        width: 60px;
        height: 60px;
        background: #1e3c72;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 1.5rem;
        font-weight: bold;
    }

    .step h4 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 10px;
        color: #1e3c72;
    }

    .step p {
        color: #666;
        line-height: 1.6;
    }

    .requirements-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .requirement-card {
        background: white;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }

    .requirement-card h4 {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .requirement-card ul {
        list-style: none;
    }

    .requirement-card li {
        padding: 8px 0;
        color: #555;
        position: relative;
        padding-left: 20px;
    }

    .requirement-card li:before {
        content: "•";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .cta-section {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
    }

    .cta-content {
        text-align: center;
        max-width: 600px;
        margin: 0 auto;
    }

    .cta-content h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: white;
    }

    .cta-content p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .cta-button-secondary {
        display: inline-block;
        background: transparent;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        border: 2px solid white;
        transition: all 0.3s ease;
    }

    .cta-button-secondary:hover {
        background: white;
        color: #1e3c72;
    }

    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .hero {
            padding: 60px 15px;
            min-height: 350px;
        }
        
        .hero h1 {
            font-size: 2.5rem;
        }
        
        .section {
            padding: 40px 0;
        }
        
        .section-title {
            font-size: 2rem;
        }
        
        .program-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .program-card {
            padding: 30px 20px;
        }
        
        .features-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        
        .process-steps {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .requirements-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .container {
            padding: 0 15px;
        }

        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }
    }

    @@media (max-width: 480px) {
        .hero h1 {
            font-size: 2rem;
        }
        
        .section-title {
            font-size: 1.8rem;
        }
        
        .features-grid {
            grid-template-columns: 1fr;
        }
        
        .program-card {
            padding: 25px 15px;
        }

        .cta-content h2 {
            font-size: 2rem;
        }
    }
</style>
