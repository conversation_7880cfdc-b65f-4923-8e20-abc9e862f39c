@page "/job-preferences"
@layout CareerPortal.Components.Layout.CareerPortalLayout
@using CareerPortal.Data.DTOs
@using CareerPortal.Services
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Authorization
@using CareerPortal.Data
@attribute [Authorize]
@inject IProfileService ProfileService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserManager<ApplicationUser> UserManager

<PageTitle>Job Notification Preferences - Career Portal</PageTitle>

<div class="preferences-page">
    <div class="preferences-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Job Notification Preferences</h1>
            <p class="page-subtitle">Configure your job alert preferences to receive notifications about relevant opportunities</p>
        </div>

        <!-- Preferences Form -->
        <div class="preferences-form-container">
            <EditForm Model="preferencesModel" OnValidSubmit="SavePreferences" class="preferences-form">
                <DataAnnotationsValidator />
                
                <div class="form-sections">
                    <!-- Job Criteria Section -->
                    <div class="form-section">
                        <h3 class="section-title">Job Search Criteria</h3>
                        <p class="section-description">Define what types of jobs you're interested in</p>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="keywords" class="form-label">Keywords</label>
                                <InputText id="keywords" @bind-Value="preferencesModel.Keywords" class="form-input" 
                                           placeholder="e.g., Software Engineer, Marketing, Finance" />
                                <ValidationMessage For="() => preferencesModel.Keywords" class="validation-message" />
                                <small class="form-help">Enter job titles, skills, or keywords separated by commas</small>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="location" class="form-label">Preferred Location</label>
                                    <select id="location" @bind="preferencesModel.LocationPreference" class="form-select">
                                        <option value="">Any Location</option>
                                        <option value="Karachi">Karachi</option>
                                        <option value="Lahore">Lahore</option>
                                        <option value="Islamabad">Islamabad</option>
                                        <option value="Faisalabad">Faisalabad</option>
                                        <option value="Rawalpindi">Rawalpindi</option>
                                        <option value="Remote">Remote</option>
                                    </select>
                                    <ValidationMessage For="() => preferencesModel.LocationPreference" class="validation-message" />
                                </div>
                                
                                <div class="form-group">
                                    <label for="jobType" class="form-label">Job Type</label>
                                    <select id="jobType" @bind="preferencesModel.JobTypePreference" class="form-select">
                                        <option value="">Any Type</option>
                                        <option value="Full-time">Full-time</option>
                                        <option value="Part-time">Part-time</option>
                                        <option value="Contract">Contract</option>
                                        <option value="Internship">Internship</option>
                                        <option value="Freelance">Freelance</option>
                                    </select>
                                    <ValidationMessage For="() => preferencesModel.JobTypePreference" class="validation-message" />
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="department" class="form-label">Department</label>
                                <select id="department" @bind="preferencesModel.DepartmentPreference" class="form-select">
                                    <option value="">Any Department</option>
                                    <option value="Information Technology">Information Technology</option>
                                    <option value="Engineering">Engineering</option>
                                    <option value="Manufacturing">Manufacturing</option>
                                    <option value="Sales">Sales</option>
                                    <option value="Marketing">Marketing</option>
                                    <option value="Finance">Finance</option>
                                    <option value="Human Resources">Human Resources</option>
                                    <option value="Operations">Operations</option>
                                    <option value="Quality Assurance">Quality Assurance</option>
                                </select>
                                <ValidationMessage For="() => preferencesModel.DepartmentPreference" class="validation-message" />
                            </div>
                        </div>
                    </div>

                    <!-- Salary Expectations Section -->
                    <div class="form-section">
                        <h3 class="section-title">Salary Expectations</h3>
                        <p class="section-description">Set your salary range preferences (optional)</p>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="minSalary" class="form-label">Minimum Salary (PKR)</label>
                                <InputNumber id="minSalary" @bind-Value="preferencesModel.MinSalary" class="form-input" 
                                             placeholder="e.g., 50000" min="0" step="5000" />
                                <ValidationMessage For="() => preferencesModel.MinSalary" class="validation-message" />
                            </div>
                            
                            <div class="form-group">
                                <label for="maxSalary" class="form-label">Maximum Salary (PKR)</label>
                                <InputNumber id="maxSalary" @bind-Value="preferencesModel.MaxSalary" class="form-input" 
                                             placeholder="e.g., 150000" min="0" step="5000" />
                                <ValidationMessage For="() => preferencesModel.MaxSalary" class="validation-message" />
                            </div>
                        </div>
                    </div>

                    <!-- Notification Settings Section -->
                    <div class="form-section">
                        <h3 class="section-title">Notification Settings</h3>
                        <p class="section-description">Choose how you want to receive job notifications</p>
                        
                        <div class="notification-options">
                            <label class="checkbox-label">
                                <InputCheckbox @bind-Value="preferencesModel.EmailNotifications" class="form-checkbox" />
                                <div class="checkbox-content">
                                    <span class="checkbox-title">Email Notifications</span>
                                    <span class="checkbox-description">Receive job alerts via email</span>
                                </div>
                            </label>
                            
                            <label class="checkbox-label">
                                <InputCheckbox @bind-Value="preferencesModel.IsActive" class="form-checkbox" />
                                <div class="checkbox-content">
                                    <span class="checkbox-title">Active Job Alerts</span>
                                    <span class="checkbox-description">Enable/disable all job notifications</span>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                        @if (isSubmitting)
                        {
                            <span class="loading-spinner"></span>
                            <span>Saving...</span>
                        }
                        else
                        {
                            <span>Save Preferences</span>
                        }
                    </button>
                    <button type="button" class="btn btn-secondary" @onclick="ResetPreferences">
                        Reset to Default
                    </button>
                </div>
            </EditForm>
        </div>

        <!-- Current Preferences Summary -->
        <div class="preferences-summary">
            <h3>Current Preferences Summary</h3>
            <div class="summary-grid">
                <div class="summary-item">
                    <span class="summary-label">Keywords:</span>
                    <span class="summary-value">@(string.IsNullOrEmpty(preferencesModel.Keywords) ? "Any" : preferencesModel.Keywords)</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Location:</span>
                    <span class="summary-value">@(string.IsNullOrEmpty(preferencesModel.LocationPreference) ? "Any" : preferencesModel.LocationPreference)</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Job Type:</span>
                    <span class="summary-value">@(string.IsNullOrEmpty(preferencesModel.JobTypePreference) ? "Any" : preferencesModel.JobTypePreference)</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Department:</span>
                    <span class="summary-value">@(string.IsNullOrEmpty(preferencesModel.DepartmentPreference) ? "Any" : preferencesModel.DepartmentPreference)</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Salary Range:</span>
                    <span class="summary-value">
                        @if (preferencesModel.MinSalary.HasValue || preferencesModel.MaxSalary.HasValue)
                        {
                            @($"PKR {preferencesModel.MinSalary?.ToString("N0") ?? "0"} - {preferencesModel.MaxSalary?.ToString("N0") ?? "∞"}")
                        }
                        else
                        {
                            @("Not specified")
                        }
                    </span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Status:</span>
                    <span class="summary-value @(preferencesModel.IsActive ? "active" : "inactive")">
                        @(preferencesModel.IsActive ? "Active" : "Inactive")
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

@if (showSuccessMessage)
{
    <div class="success-toast">
        <span class="toast-icon">✅</span>
        <span>Job preferences saved successfully!</span>
    </div>
}

@if (showErrorMessage)
{
    <div class="error-toast">
        <span class="toast-icon">❌</span>
        <span>@errorMessage</span>
    </div>
}

<style>
    .preferences-page {
        background-color: #f8f9fa;
        min-height: calc(100vh - 70px);
        padding: 40px 0;
    }

    .preferences-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .page-header {
        text-align: center;
        margin-bottom: 40px;
    }

    .page-title {
        font-size: 2.5rem;
        color: #1e3c72;
        margin-bottom: 10px;
        font-weight: 700;
    }

    .page-subtitle {
        font-size: 1.1rem;
        color: #666;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }

    .preferences-form-container {
        background: white;
        border-radius: 12px;
        padding: 40px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .form-sections {
        display: flex;
        flex-direction: column;
        gap: 40px;
    }

    .form-section {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 30px;
    }

    .form-section:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .section-title {
        color: #1e3c72;
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .section-description {
        color: #666;
        margin-bottom: 25px;
        font-size: 1rem;
    }

    .form-grid {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .form-label {
        font-weight: 500;
        color: #333;
        font-size: 0.95rem;
    }

    .form-input,
    .form-select {
        padding: 12px 16px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 1rem;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
        background: white;
    }

    .form-input:focus,
    .form-select:focus {
        outline: none;
        border-color: #1e3c72;
        box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
    }

    .form-help {
        color: #666;
        font-size: 0.85rem;
        font-style: italic;
    }

    .validation-message {
        color: #dc3545;
        font-size: 0.85rem;
    }

    .notification-options {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .checkbox-label {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        cursor: pointer;
        padding: 15px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .checkbox-label:hover {
        border-color: #1e3c72;
        background-color: #f8f9fa;
    }

    .form-checkbox {
        width: 20px;
        height: 20px;
        margin-top: 2px;
    }

    .checkbox-content {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .checkbox-title {
        font-weight: 500;
        color: #333;
        font-size: 1rem;
    }

    .checkbox-description {
        color: #666;
        font-size: 0.9rem;
    }

    .form-actions {
        display: flex;
        gap: 15px;
        margin-top: 40px;
        padding-top: 30px;
        border-top: 1px solid #e9ecef;
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 140px;
        justify-content: center;
    }

    .btn-primary {
        background-color: #1e3c72;
        color: white;
    }

    .btn-primary:hover:not(:disabled) {
        background-color: #2a5298;
        transform: translateY(-1px);
    }

    .btn-primary:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        transform: translateY(-1px);
    }

    .loading-spinner {
        width: 16px;
        height: 16px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .preferences-summary {
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .preferences-summary h3 {
        color: #1e3c72;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
    }

    .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .summary-label {
        font-weight: 500;
        color: #666;
    }

    .summary-value {
        color: #333;
        font-weight: 500;
    }

    .summary-value.active {
        color: #28a745;
    }

    .summary-value.inactive {
        color: #dc3545;
    }

    .success-toast,
    .error-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 10px;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    }

    .success-toast {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .error-toast {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    @@keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .preferences-container {
            padding: 0 15px;
        }

        .page-title {
            font-size: 2rem;
        }

        .preferences-form-container {
            padding: 25px;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }

        .btn {
            width: 100%;
        }

        .summary-grid {
            grid-template-columns: 1fr;
        }

        .success-toast,
        .error-toast {
            right: 15px;
            left: 15px;
            top: 15px;
        }
    }

    @@media (max-width: 480px) {
        .preferences-page {
            padding: 20px 0;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .preferences-form-container {
            padding: 20px;
        }

        .preferences-summary {
            padding: 20px;
        }
    }
</style>

@code {
    private string currentUserId = "";
    private JobAlertPreferenceDto preferencesModel = new();
    private bool isSubmitting = false;
    private bool showSuccessMessage = false;
    private bool showErrorMessage = false;
    private string errorMessage = "";

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = await UserManager.GetUserAsync(authState.User);
        
        if (user != null)
        {
            currentUserId = user.Id;
            await LoadPreferences();
        }
    }

    private async Task LoadPreferences()
    {
        try
        {
            var preferences = await ProfileService.GetJobAlertPreferencesAsync(currentUserId);
            if (preferences?.Any() == true)
            {
                preferencesModel = preferences.First();
            }
            else
            {
                // Set default values
                preferencesModel = new JobAlertPreferenceDto
                {
                    EmailNotifications = true,
                    IsActive = true
                };
            }
        }
        catch (Exception ex)
        {
            await ShowError("Failed to load job preferences.");
        }
    }

    private async Task SavePreferences()
    {
        isSubmitting = true;
        try
        {
            var preferences = new List<JobAlertPreferenceDto> { preferencesModel };
            var success = await ProfileService.UpdateJobAlertPreferencesAsync(currentUserId, preferences);
            
            if (success)
            {
                await ShowSuccess();
            }
            else
            {
                await ShowError("Failed to save job preferences.");
            }
        }
        catch (Exception ex)
        {
            await ShowError("An error occurred while saving preferences.");
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void ResetPreferences()
    {
        preferencesModel = new JobAlertPreferenceDto
        {
            EmailNotifications = true,
            IsActive = true
        };
    }

    private async Task ShowSuccess()
    {
        showSuccessMessage = true;
        StateHasChanged();
        await Task.Delay(3000);
        showSuccessMessage = false;
        StateHasChanged();
    }

    private async Task ShowError(string message)
    {
        errorMessage = message;
        showErrorMessage = true;
        StateHasChanged();
        await Task.Delay(3000);
        showErrorMessage = false;
        StateHasChanged();
    }
}
