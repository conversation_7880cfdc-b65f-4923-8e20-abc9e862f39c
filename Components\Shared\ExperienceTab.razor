@using CareerPortal.Data.DTOs
@using System.ComponentModel.DataAnnotations
@inject IJSRuntime JSRuntime

<div class="tab-panel">
    <div class="section-header">
        <div>
            <h3>Work Experience</h3>
            <p class="section-description">Add your professional work experience</p>
        </div>
        <button class="btn btn-primary" @onclick="ShowAddForm">
            <span class="btn-icon">+</span>
            Add Experience
        </button>
    </div>

    <!-- Experience List -->
    <div class="experience-list">
        @if (Experiences?.Any() == true)
        {
            @foreach (var experience in Experiences)
            {
                <div class="experience-card">
                    <div class="experience-header">
                        <div class="experience-info">
                            <h4 class="job-title">@experience.JobTitle</h4>
                            <p class="company-name">@experience.CompanyName</p>
                            <div class="experience-meta">
                                @if (!string.IsNullOrEmpty(experience.Location))
                                {
                                    <span class="meta-item">📍 @experience.Location</span>
                                }
                                <span class="meta-item">
                                    📅 @experience.StartDate?.ToString("MMM yyyy") - 
                                    @(experience.IsCurrentJob ? "Present" : experience.EndDate?.ToString("MMM yyyy"))
                                </span>
                                @if (experience.IsCurrentJob)
                                {
                                    <span class="current-job-badge">Current</span>
                                }
                            </div>
                        </div>
                        <div class="experience-actions">
                            <button class="btn-icon-small" @onclick="() => EditExperience(experience)" title="Edit">
                                ✏️
                            </button>
                            <button class="btn-icon-small delete" @onclick="() => DeleteExperience(experience.Id)" title="Delete">
                                🗑️
                            </button>
                        </div>
                    </div>
                    
                    @if (!string.IsNullOrEmpty(experience.JobDescription))
                    {
                        <div class="experience-description">
                            <h5>Job Description:</h5>
                            <p>@experience.JobDescription</p>
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(experience.KeyAchievements))
                    {
                        <div class="experience-achievements">
                            <h5>Key Achievements:</h5>
                            <p>@experience.KeyAchievements</p>
                        </div>
                    }
                </div>
            }
        }
        else
        {
            <div class="empty-state">
                <div class="empty-icon">💼</div>
                <h4>No work experience added yet</h4>
                <p>Add your professional work experience to showcase your career journey</p>
                <button class="btn btn-primary" @onclick="ShowAddForm">
                    Add Your First Experience
                </button>
            </div>
        }
    </div>

    <!-- Add/Edit Form Modal -->
    @if (showForm)
    {
        <div class="modal-overlay" @onclick="HideForm">
            <div class="modal-content" @onclick:stopPropagation="true">
                <div class="modal-header">
                    <h4>@(isEditing ? "Edit" : "Add") Work Experience</h4>
                    <button class="modal-close" @onclick="HideForm">×</button>
                </div>
                
                <EditForm Model="editModel" OnValidSubmit="HandleValidSubmit" class="experience-form">
                    <DataAnnotationsValidator />
                    
                    <div class="form-grid">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="jobTitle" class="form-label">Job Title *</label>
                                <InputText id="jobTitle" @bind-Value="editModel.JobTitle" class="form-input" placeholder="e.g., Software Engineer" />
                                <ValidationMessage For="() => editModel.JobTitle" class="validation-message" />
                            </div>
                            <div class="form-group">
                                <label for="companyName" class="form-label">Company Name *</label>
                                <InputText id="companyName" @bind-Value="editModel.CompanyName" class="form-input" placeholder="e.g., Pak Suzuki" />
                                <ValidationMessage For="() => editModel.CompanyName" class="validation-message" />
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="location" class="form-label">Location</label>
                            <InputText id="location" @bind-Value="editModel.Location" class="form-input" placeholder="e.g., Karachi, Pakistan" />
                            <ValidationMessage For="() => editModel.Location" class="validation-message" />
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="startDate" class="form-label">Start Date *</label>
                                <InputDate id="startDate" @bind-Value="editModel.StartDate" class="form-input" />
                                <ValidationMessage For="() => editModel.StartDate" class="validation-message" />
                            </div>
                            <div class="form-group">
                                <label for="endDate" class="form-label">End Date</label>
                                <InputDate id="endDate" @bind-Value="editModel.EndDate" class="form-input" disabled="@editModel.IsCurrentJob" />
                                <ValidationMessage For="() => editModel.EndDate" class="validation-message" />
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <InputCheckbox @bind-Value="editModel.IsCurrentJob" class="form-checkbox" />
                                <span class="checkbox-text">This is my current job</span>
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label for="jobDescription" class="form-label">Job Description</label>
                            <InputTextArea id="jobDescription" @bind-Value="editModel.JobDescription" class="form-textarea" 
                                           placeholder="Describe your role and responsibilities..." rows="4" />
                            <ValidationMessage For="() => editModel.JobDescription" class="validation-message" />
                        </div>
                        
                        <div class="form-group">
                            <label for="keyAchievements" class="form-label">Key Achievements</label>
                            <InputTextArea id="keyAchievements" @bind-Value="editModel.KeyAchievements" class="form-textarea" 
                                           placeholder="Highlight your key achievements and accomplishments..." rows="3" />
                            <ValidationMessage For="() => editModel.KeyAchievements" class="validation-message" />
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="loading-spinner"></span>
                                <span>Saving...</span>
                            }
                            else
                            {
                                <span>@(isEditing ? "Update" : "Add") Experience</span>
                            }
                        </button>
                        <button type="button" class="btn btn-secondary" @onclick="HideForm">
                            Cancel
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    }
</div>

<style>
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        gap: 20px;
    }

    .section-description {
        color: #666;
        margin-top: 5px;
        font-size: 1rem;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary {
        background-color: #1e3c72;
        color: white;
    }

    .btn-primary:hover {
        background-color: #2a5298;
        transform: translateY(-1px);
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
    }

    .btn-icon {
        font-size: 1.2rem;
        font-weight: bold;
    }

    .experience-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .experience-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 25px;
        transition: box-shadow 0.3s ease;
    }

    .experience-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .experience-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
    }

    .job-title {
        color: #1e3c72;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .company-name {
        color: #333;
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 10px;
    }

    .experience-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        align-items: center;
    }

    .meta-item {
        color: #666;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .current-job-badge {
        background-color: #28a745;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .experience-actions {
        display: flex;
        gap: 8px;
    }

    .btn-icon-small {
        background: none;
        border: none;
        padding: 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 1rem;
        transition: background-color 0.3s ease;
    }

    .btn-icon-small:hover {
        background-color: #e9ecef;
    }

    .btn-icon-small.delete:hover {
        background-color: #f8d7da;
    }

    .experience-description,
    .experience-achievements {
        margin-top: 15px;
    }

    .experience-description h5,
    .experience-achievements h5 {
        color: #1e3c72;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .experience-description p,
    .experience-achievements p {
        color: #555;
        line-height: 1.6;
        margin: 0;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .empty-icon {
        font-size: 4rem;
        margin-bottom: 20px;
    }

    .empty-state h4 {
        color: #1e3c72;
        margin-bottom: 10px;
    }

    .empty-state p {
        margin-bottom: 30px;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        padding: 20px;
    }

    .modal-content {
        background: white;
        border-radius: 12px;
        width: 100%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 25px;
        border-bottom: 1px solid #e9ecef;
    }

    .modal-header h4 {
        color: #1e3c72;
        margin: 0;
        font-size: 1.3rem;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;
        padding: 5px;
        border-radius: 4px;
    }

    .modal-close:hover {
        background-color: #f8f9fa;
    }

    .experience-form {
        padding: 25px;
    }

    .form-grid {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .form-label {
        font-weight: 500;
        color: #333;
        font-size: 0.95rem;
    }

    .form-input,
    .form-textarea {
        padding: 12px 16px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 1rem;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .form-input:focus,
    .form-textarea:focus {
        outline: none;
        border-color: #1e3c72;
        box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
    }

    .form-textarea {
        resize: vertical;
        min-height: 80px;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        font-size: 0.95rem;
    }

    .form-checkbox {
        width: 18px;
        height: 18px;
    }

    .checkbox-text {
        color: #333;
        font-weight: 500;
    }

    .validation-message {
        color: #dc3545;
        font-size: 0.85rem;
    }

    .form-actions {
        display: flex;
        gap: 15px;
        margin-top: 25px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }

    .loading-spinner {
        width: 16px;
        height: 16px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .section-header {
            flex-direction: column;
            align-items: stretch;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .experience-header {
            flex-direction: column;
            gap: 15px;
        }

        .experience-actions {
            align-self: flex-start;
        }

        .modal-content {
            margin: 10px;
            max-height: calc(100vh - 20px);
        }

        .form-actions {
            flex-direction: column;
        }
    }
</style>

@code {
    [Parameter] public List<ExperienceDto> Experiences { get; set; } = new();
    [Parameter] public EventCallback<ExperienceDto> OnAdd { get; set; }
    [Parameter] public EventCallback<ExperienceDto> OnEdit { get; set; }
    [Parameter] public EventCallback<int> OnDelete { get; set; }

    private bool showForm = false;
    private bool isEditing = false;
    private bool isSubmitting = false;
    private ExperienceDto editModel = new();

    private void ShowAddForm()
    {
        editModel = new ExperienceDto { StartDate = DateTime.Today };
        isEditing = false;
        showForm = true;
    }

    private void EditExperience(ExperienceDto experience)
    {
        editModel = new ExperienceDto
        {
            Id = experience.Id,
            JobTitle = experience.JobTitle,
            CompanyName = experience.CompanyName,
            Location = experience.Location,
            StartDate = experience.StartDate,
            EndDate = experience.EndDate,
            IsCurrentJob = experience.IsCurrentJob,
            JobDescription = experience.JobDescription,
            KeyAchievements = experience.KeyAchievements
        };
        isEditing = true;
        showForm = true;
    }

    private async Task DeleteExperience(int experienceId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this experience?"))
        {
            await OnDelete.InvokeAsync(experienceId);
        }
    }

    private void HideForm()
    {
        showForm = false;
        editModel = new();
    }

    private async Task HandleValidSubmit()
    {
        isSubmitting = true;
        try
        {
            if (isEditing)
            {
                await OnEdit.InvokeAsync(editModel);
            }
            else
            {
                await OnAdd.InvokeAsync(editModel);
            }
            HideForm();
        }
        finally
        {
            isSubmitting = false;
        }
    }
}
