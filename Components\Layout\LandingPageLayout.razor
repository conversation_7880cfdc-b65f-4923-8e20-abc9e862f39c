@inherits LayoutComponentBase
@using CareerPortal.Data

<link href="~/css/landing.css" rel="stylesheet" />
<style>
    /* Reset FluentUI interference for landing page */
    .landing-page {
        width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
        font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif !important;
        line-height: 1.6 !important;
        color: #333 !important;
    }

    .landing-page .hero {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
        color: white !important;
        padding: 80px 20px !important;
        text-align: center !important;
        min-height: 500px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        flex-direction: column !important;
    }

    .landing-page .hero h1 {
        font-size: 3.5rem !important;
        font-weight: 700 !important;
        margin-bottom: 20px !important;
        color: white !important;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3) !important;
    }

    .landing-page .hero p {
        font-size: 1.3rem !important;
        margin-bottom: 30px !important;
        color: white !important;
        opacity: 0.95 !important;
    }

    .landing-page .cta-button {
        display: inline-block !important;
        background: #ff6b35 !important;
        color: white !important;
        padding: 15px 35px !important;
        text-decoration: none !important;
        border-radius: 50px !important;
        font-weight: 600 !important;
        font-size: 1.1rem !important;
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3) !important;
        transition: all 0.3s ease !important;
    }

    .landing-page .cta-button:hover {
        background: #e55a2b !important;
        color: white !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4) !important;
    }

    .landing-page .section {
        padding: 60px 0 !important;
        width: 100% !important;
    }

    .landing-page .container {
        max-width: 1200px !important;
        margin: 0 auto !important;
        padding: 0 20px !important;
    }

    .landing-page .section-title {
        text-align: center !important;
        font-size: 2.5rem !important;
        font-weight: 700 !important;
        margin-bottom: 20px !important;
        color: #1e3c72 !important;
    }

    .landing-page .section-subtitle {
        text-align: center !important;
        font-size: 1.2rem !important;
        color: #666 !important;
        margin-bottom: 50px !important;
        max-width: 600px !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }

    .landing-page .program-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
        gap: 30px !important;
        margin-top: 40px !important;
    }

    .landing-page .program-card {
        background: white !important;
        border-radius: 15px !important;
        padding: 40px 30px !important;
        text-align: center !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
        transition: all 0.3s ease !important;
        border: 1px solid #f0f0f0 !important;
    }

    .landing-page .program-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 15px 40px rgba(0,0,0,0.15) !important;
    }

    .landing-page .program-icon {
        width: 80px !important;
        height: 80px !important;
        background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 auto 25px !important;
        font-size: 2rem !important;
        color: white !important;
    }

    .landing-page .program-card h3 {
        font-size: 1.5rem !important;
        font-weight: 600 !important;
        margin-bottom: 15px !important;
        color: #1e3c72 !important;
    }

    .landing-page .program-card p {
        color: #666 !important;
        margin-bottom: 25px !important;
        line-height: 1.6 !important;
    }

    .landing-page .learn-more {
        color: #1e3c72 !important;
        text-decoration: none !important;
        font-weight: 600 !important;
        border-bottom: 2px solid transparent !important;
        transition: border-color 0.3s ease !important;
    }

    .landing-page .learn-more:hover {
        border-bottom-color: #1e3c72 !important;
        color: #1e3c72 !important;
    }

    /* Jobs Grid */
    .landing-page .jobs-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
        gap: 25px !important;
        margin-top: 40px !important;
    }

    .landing-page .job-card {
        background: white !important;
        border-radius: 12px !important;
        padding: 30px !important;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08) !important;
        transition: all 0.3s ease !important;
        border-left: 4px solid #ff6b35 !important;
    }

    .landing-page .job-card:hover {
        transform: translateY(-3px) !important;
        box-shadow: 0 8px 25px rgba(0,0,0,0.12) !important;
    }

    .landing-page .job-title {
        font-size: 1.3rem !important;
        font-weight: 600 !important;
        color: #1e3c72 !important;
        margin-bottom: 10px !important;
    }

    .landing-page .job-meta {
        display: flex !important;
        gap: 15px !important;
        margin-bottom: 15px !important;
        font-size: 0.9rem !important;
        color: #666 !important;
    }

    .landing-page .job-description {
        color: #555 !important;
        margin-bottom: 20px !important;
        line-height: 1.6 !important;
    }

    .landing-page .apply-button {
        background: #1e3c72 !important;
        color: white !important;
        padding: 10px 20px !important;
        border: none !important;
        border-radius: 6px !important;
        text-decoration: none !important;
        display: inline-block !important;
        font-weight: 500 !important;
        transition: background 0.3s ease !important;
    }

    .landing-page .apply-button:hover {
        background: #2a5298 !important;
        color: white !important;
    }

    /* Life Section */
    .landing-page .life-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    }

    .landing-page .features-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
        gap: 30px !important;
        margin-top: 40px !important;
    }

    .landing-page .feature-item {
        text-align: center !important;
        padding: 20px !important;
    }

    .landing-page .feature-icon {
        width: 60px !important;
        height: 60px !important;
        background: #ff6b35 !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 auto 20px !important;
        color: white !important;
        font-size: 1.5rem !important;
    }

    .landing-page .feature-item h4 {
        font-size: 1.2rem !important;
        font-weight: 600 !important;
        margin-bottom: 10px !important;
        color: #1e3c72 !important;
    }

    .landing-page .feature-item p {
        color: #666 !important;
        font-size: 0.95rem !important;
        line-height: 1.6 !important;
    }

    /* Footer */
    .landing-page .footer {
        background: #1e3c72 !important;
        color: white !important;
        padding: 50px 0 20px !important;
    }

    .landing-page .footer-content {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
        gap: 40px !important;
        margin-bottom: 30px !important;
    }

    .landing-page .footer-section h4 {
        font-size: 1.3rem !important;
        font-weight: 600 !important;
        margin-bottom: 20px !important;
        color: #ff6b35 !important;
    }

    .landing-page .footer-section ul {
        list-style: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    .landing-page .footer-section ul li {
        margin-bottom: 10px !important;
    }

    .landing-page .footer-section ul li a {
        color: #ccc !important;
        text-decoration: none !important;
        transition: color 0.3s ease !important;
    }

    .landing-page .footer-section ul li a:hover {
        color: white !important;
    }

    .landing-page .footer-bottom {
        border-top: 1px solid #2a5298 !important;
        padding-top: 20px !important;
        text-align: center !important;
        color: #ccc !important;
    }

    .landing-page .footer-bottom a {
        color: #ccc !important;
        text-decoration: none !important;
    }

    .landing-page .footer-bottom a:hover {
        color: white !important;
    }

    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .landing-page .hero {
            padding: 60px 15px !important;
            min-height: 400px !important;
        }

        .landing-page .hero h1 {
            font-size: 2.5rem !important;
        }

        .landing-page .hero p {
            font-size: 1.1rem !important;
        }

        .landing-page .section {
            padding: 40px 0 !important;
        }

        .landing-page .section-title {
            font-size: 2rem !important;
        }

        .landing-page .program-grid {
            grid-template-columns: 1fr !important;
            gap: 20px !important;
        }

        .landing-page .jobs-grid {
            grid-template-columns: 1fr !important;
            gap: 20px !important;
        }

        .landing-page .job-meta {
            flex-direction: column !important;
            gap: 5px !important;
        }

        .landing-page .features-grid {
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 20px !important;
        }

        .landing-page .footer-content {
            grid-template-columns: 1fr !important;
            gap: 30px !important;
        }

        .landing-page .container {
            padding: 0 15px !important;
        }
    }

    @@media (max-width: 480px) {
        .landing-page .hero h1 {
            font-size: 2rem !important;
        }

        .landing-page .section-title {
            font-size: 1.8rem !important;
        }

        .landing-page .features-grid {
            grid-template-columns: 1fr !important;
        }

        .landing-page .program-card {
            padding: 25px 15px !important;
        }

        .landing-page .job-card {
            padding: 20px !important;
        }
    }
</style>

<div class="landing-page">
    @Body
</div>

<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

