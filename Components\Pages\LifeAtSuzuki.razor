@page "/life-at-suzuki"
@layout CareerPortal.Components.Layout.CareerPortalLayout

<PageTitle>Life at Suzuki - Pak Suzuki Career Portal</PageTitle>

<!-- Hero Section -->
<section class="hero">
    <div class="hero-content">
        <h1>Life at Pak Suzuki</h1>
        <p>Discover what makes Pak Suzuki a great place to work. Join a culture of excellence, innovation, and growth where every team member contributes to our shared success.</p>
        <a href="#culture" class="cta-button">Explore Our Culture</a>
    </div>
</section>

<!-- Company Culture Section -->
<section class="section" id="culture">
    <div class="container">
        <h2 class="section-title">Our Culture & Values</h2>
        <p class="section-subtitle">Built on a foundation of excellence, integrity, and innovation</p>

        <div class="values-grid">
            <div class="value-card">
                <div class="value-icon">🏆</div>
                <h3>Excellence</h3>
                <p>We strive for excellence in everything we do, from product quality to customer service, setting the highest standards in the automotive industry.</p>
            </div>

            <div class="value-card">
                <div class="value-icon">🤝</div>
                <h3>Teamwork</h3>
                <p>Collaboration and mutual respect drive our success. We believe that diverse perspectives and collective effort lead to better outcomes.</p>
            </div>

            <div class="value-card">
                <div class="value-icon">💡</div>
                <h3>Innovation</h3>
                <p>We embrace change and continuously seek innovative solutions to meet evolving customer needs and market challenges.</p>
            </div>

            <div class="value-card">
                <div class="value-icon">🎯</div>
                <h3>Integrity</h3>
                <p>Honesty, transparency, and ethical behavior are the cornerstones of our business practices and relationships.</p>
            </div>

            <div class="value-card">
                <div class="value-icon">🌱</div>
                <h3>Growth</h3>
                <p>We invest in our people's development and provide opportunities for continuous learning and career advancement.</p>
            </div>

            <div class="value-card">
                <div class="value-icon">🌍</div>
                <h3>Sustainability</h3>
                <p>We are committed to environmental responsibility and sustainable business practices for future generations.</p>
            </div>
        </div>
    </div>
</section>

<!-- Employee Benefits Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Employee Benefits & Perks</h2>
        <p class="section-subtitle">Comprehensive benefits package designed to support your well-being and success</p>

        <div class="benefits-categories">
            <div class="benefit-category">
                <h3>Health & Wellness</h3>
                <div class="benefits-list">
                    <div class="benefit-item">
                        <div class="benefit-icon">🏥</div>
                        <div class="benefit-content">
                            <h4>Medical Insurance</h4>
                            <p>Comprehensive health coverage for employees and their families</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">🏃</div>
                        <div class="benefit-content">
                            <h4>Fitness Programs</h4>
                            <p>On-site gym facilities and wellness programs</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">🧘</div>
                        <div class="benefit-content">
                            <h4>Mental Health Support</h4>
                            <p>Counseling services and stress management programs</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="benefit-category">
                <h3>Financial Security</h3>
                <div class="benefits-list">
                    <div class="benefit-item">
                        <div class="benefit-icon">💰</div>
                        <div class="benefit-content">
                            <h4>Competitive Salary</h4>
                            <p>Market-competitive compensation packages</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">📈</div>
                        <div class="benefit-content">
                            <h4>Performance Bonuses</h4>
                            <p>Annual performance-based incentives and bonuses</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">🏦</div>
                        <div class="benefit-content">
                            <h4>Retirement Plans</h4>
                            <p>Provident fund and retirement savings programs</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="benefit-category">
                <h3>Work-Life Balance</h3>
                <div class="benefits-list">
                    <div class="benefit-item">
                        <div class="benefit-icon">🏖️</div>
                        <div class="benefit-content">
                            <h4>Paid Time Off</h4>
                            <p>Generous vacation days and personal leave policies</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">⏰</div>
                        <div class="benefit-content">
                            <h4>Flexible Hours</h4>
                            <p>Flexible working arrangements and remote work options</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">👶</div>
                        <div class="benefit-content">
                            <h4>Family Support</h4>
                            <p>Maternity/paternity leave and childcare assistance</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Career Development Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Career Development</h2>
        <p class="section-subtitle">Invest in your future with comprehensive learning and development opportunities</p>

        <div class="development-areas">
            <div class="development-card">
                <div class="dev-icon">📚</div>
                <h3>Learning & Training</h3>
                <ul class="dev-features">
                    <li>Technical skills training programs</li>
                    <li>Leadership development workshops</li>
                    <li>Industry certification support</li>
                    <li>Online learning platforms access</li>
                    <li>Cross-functional training opportunities</li>
                </ul>
            </div>

            <div class="development-card">
                <div class="dev-icon">🎯</div>
                <h3>Career Progression</h3>
                <ul class="dev-features">
                    <li>Clear career advancement paths</li>
                    <li>Regular performance reviews</li>
                    <li>Mentorship programs</li>
                    <li>Internal job posting priority</li>
                    <li>Succession planning initiatives</li>
                </ul>
            </div>

            <div class="development-card">
                <div class="dev-icon">🌐</div>
                <h3>Global Exposure</h3>
                <ul class="dev-features">
                    <li>International assignment opportunities</li>
                    <li>Global project participation</li>
                    <li>Cross-cultural training</li>
                    <li>International conference attendance</li>
                    <li>Best practice sharing sessions</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Work Environment Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Work Environment</h2>
        <p class="section-subtitle">Modern facilities and collaborative spaces designed for productivity and comfort</p>

        <div class="environment-features">
            <div class="env-feature">
                <div class="env-image">🏢</div>
                <h3>Modern Facilities</h3>
                <p>State-of-the-art office buildings with modern amenities, comfortable workspaces, and advanced technology infrastructure.</p>
            </div>

            <div class="env-feature">
                <div class="env-image">🤝</div>
                <h3>Collaborative Spaces</h3>
                <p>Open-plan offices, meeting rooms, and collaboration areas designed to foster teamwork and creative thinking.</p>
            </div>

            <div class="env-feature">
                <div class="env-image">🍽️</div>
                <h3>Dining & Recreation</h3>
                <p>On-site cafeterias, recreational areas, and social spaces for relaxation and team building activities.</p>
            </div>

            <div class="env-feature">
                <div class="env-image">🚗</div>
                <h3>Transportation</h3>
                <p>Company transportation services, parking facilities, and convenient location access for all employees.</p>
            </div>

            <div class="env-feature">
                <div class="env-image">🔒</div>
                <h3>Safety & Security</h3>
                <p>Comprehensive safety protocols, security systems, and health & safety training for all team members.</p>
            </div>

            <div class="env-feature">
                <div class="env-image">🌿</div>
                <h3>Green Initiatives</h3>
                <p>Environmentally friendly practices, energy-efficient systems, and sustainability programs throughout our facilities.</p>
            </div>
        </div>
    </div>
</section>

<!-- Employee Testimonials Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">What Our Employees Say</h2>
        <p class="section-subtitle">Hear from our team members about their experiences at Pak Suzuki</p>

        <div class="testimonials-grid">
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <p>"Working at Pak Suzuki has been an incredible journey. The company truly invests in its people, and I've grown both professionally and personally. The collaborative culture and growth opportunities are exceptional."</p>
                </div>
                <div class="testimonial-author">
                    <div class="author-info">
                        <h4>Ahmed Hassan</h4>
                        <span>Senior Engineer</span>
                        <span class="tenure">5 years at Suzuki</span>
                    </div>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="testimonial-content">
                    <p>"The work-life balance at Pak Suzuki is outstanding. Management understands the importance of family time, and the flexible working arrangements have made a huge difference in my life."</p>
                </div>
                <div class="testimonial-author">
                    <div class="author-info">
                        <h4>Fatima Khan</h4>
                        <span>Marketing Manager</span>
                        <span class="tenure">3 years at Suzuki</span>
                    </div>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="testimonial-content">
                    <p>"The learning opportunities here are endless. From technical training to leadership development, Pak Suzuki has supported my career growth every step of the way. I'm proud to be part of this team."</p>
                </div>
                <div class="testimonial-author">
                    <div class="author-info">
                        <h4>Muhammad Ali</h4>
                        <span>Operations Supervisor</span>
                        <span class="tenure">7 years at Suzuki</span>
                    </div>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="testimonial-content">
                    <p>"As a fresh graduate, I was welcomed with open arms and given meaningful responsibilities from day one. The mentorship and support I received helped me build confidence and develop my skills rapidly."</p>
                </div>
                <div class="testimonial-author">
                    <div class="author-info">
                        <h4>Sarah Ahmed</h4>
                        <span>Business Analyst</span>
                        <span class="tenure">2 years at Suzuki</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Diversity & Inclusion Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Diversity & Inclusion</h2>
        <p class="section-subtitle">Building an inclusive workplace where everyone can thrive</p>

        <div class="diversity-content">
            <div class="diversity-text">
                <h3>Our Commitment</h3>
                <p>At Pak Suzuki, we believe that diversity drives innovation and excellence. We are committed to creating an inclusive environment where people from all backgrounds, cultures, and perspectives can contribute their unique talents and ideas.</p>
                
                <h4>Our Initiatives:</h4>
                <ul class="diversity-list">
                    <li>Equal opportunity employment practices</li>
                    <li>Women in leadership development programs</li>
                    <li>Cultural awareness and sensitivity training</li>
                    <li>Employee resource groups and networks</li>
                    <li>Inclusive hiring and promotion practices</li>
                    <li>Accessibility and accommodation support</li>
                </ul>
            </div>
            
            <div class="diversity-stats">
                <div class="diversity-stat">
                    <div class="stat-number">40%</div>
                    <div class="stat-label">Women in Workforce</div>
                </div>
                <div class="diversity-stat">
                    <div class="stat-number">25%</div>
                    <div class="stat-label">Women in Leadership</div>
                </div>
                <div class="diversity-stat">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">Nationalities</div>
                </div>
                <div class="diversity-stat">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Equal Pay Commitment</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Join Us Section -->
<section class="section cta-section">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Join Our Team?</h2>
            <p>Become part of Pakistan's leading automotive company and build a rewarding career with us. Explore our current opportunities and take the next step in your professional journey.</p>
            <div class="cta-buttons">
                <a href="/jobs" class="cta-button">View Open Positions</a>
                <a href="/internships" class="cta-button-secondary">Explore Internships</a>
            </div>
        </div>
    </div>
</section>

<style>
    /* Base styles */
    .hero {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 80px 20px;
        text-align: center;
        position: relative;
        min-height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 800px;
    }

    .hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        color: white;
    }

    .hero p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-button {
        display: inline-block;
        background: #ff6b35;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    }

    .cta-button:hover {
        background: #e55a2b;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        color: white;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section {
        padding: 60px 0;
        width: 100%;
    }

    .section-title {
        text-align: center;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .section-subtitle {
        text-align: center;
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 50px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .values-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .value-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: 1px solid #f0f0f0;
    }

    .value-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .value-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 1.8rem;
        color: white;
    }

    .value-card h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #1e3c72;
    }

    .value-card p {
        color: #666;
        line-height: 1.6;
    }

    .benefits-categories {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 40px;
        margin-top: 40px;
    }

    .benefit-category h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 25px;
        color: #1e3c72;
        text-align: center;
    }

    .benefits-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .benefit-item {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .benefit-icon {
        width: 50px;
        height: 50px;
        background: #e8f4f8;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
        flex-shrink: 0;
    }

    .benefit-content h4 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e3c72;
        margin-bottom: 5px;
    }

    .benefit-content p {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.5;
        margin: 0;
    }

    .development-areas {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .development-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .development-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .dev-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, #1e3c72, #2a5298);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 1.8rem;
        color: white;
    }

    .development-card h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .dev-features {
        list-style: none;
        padding-left: 0;
        text-align: left;
    }

    .dev-features li {
        padding: 8px 0;
        color: #555;
        position: relative;
        padding-left: 20px;
    }

    .dev-features li:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .environment-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .env-feature {
        text-align: center;
        padding: 20px;
    }

    .env-image {
        width: 80px;
        height: 80px;
        background: #e8f4f8;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 2rem;
    }

    .env-feature h3 {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #1e3c72;
    }

    .env-feature p {
        color: #666;
        line-height: 1.6;
    }

    .testimonials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .testimonial-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-left: 4px solid #ff6b35;
    }

    .testimonial-content p {
        color: #555;
        line-height: 1.6;
        margin-bottom: 20px;
        font-style: italic;
    }

    .author-info h4 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e3c72;
        margin-bottom: 5px;
    }

    .author-info span {
        display: block;
        color: #666;
        font-size: 0.9rem;
    }

    .tenure {
        color: #ff6b35 !important;
        font-weight: 500;
    }

    .diversity-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 50px;
        align-items: start;
        margin-top: 40px;
    }

    .diversity-text h3 {
        font-size: 1.8rem;
        color: #1e3c72;
        margin-bottom: 20px;
    }

    .diversity-text h4 {
        font-size: 1.3rem;
        color: #1e3c72;
        margin: 25px 0 15px 0;
    }

    .diversity-text p {
        color: #666;
        line-height: 1.7;
        margin-bottom: 20px;
    }

    .diversity-list {
        list-style: none;
        padding-left: 0;
    }

    .diversity-list li {
        padding: 8px 0;
        color: #555;
        position: relative;
        padding-left: 25px;
    }

    .diversity-list li:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .diversity-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .diversity-stat {
        text-align: center;
        padding: 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .diversity-stat .stat-number {
        font-size: 2.2rem;
        font-weight: bold;
        color: #ff6b35;
        margin-bottom: 5px;
    }

    .diversity-stat .stat-label {
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
    }

    .cta-section {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
    }

    .cta-content {
        text-align: center;
        max-width: 600px;
        margin: 0 auto;
    }

    .cta-content h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: white;
    }

    .cta-content p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .cta-button-secondary {
        display: inline-block;
        background: transparent;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        border: 2px solid white;
        transition: all 0.3s ease;
    }

    .cta-button-secondary:hover {
        background: white;
        color: #1e3c72;
    }

    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .hero {
            padding: 60px 15px;
            min-height: 350px;
        }
        
        .hero h1 {
            font-size: 2.5rem;
        }
        
        .section {
            padding: 40px 0;
        }
        
        .section-title {
            font-size: 2rem;
        }
        
        .values-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .benefits-categories {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .development-areas {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .environment-features {
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        
        .testimonials-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .diversity-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .diversity-stats {
            grid-template-columns: 1fr 1fr;
        }
        
        .container {
            padding: 0 15px;
        }

        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }
    }

    @@media (max-width: 480px) {
        .hero h1 {
            font-size: 2rem;
        }
        
        .section-title {
            font-size: 1.8rem;
        }
        
        .environment-features {
            grid-template-columns: 1fr;
        }
        
        .diversity-stats {
            grid-template-columns: 1fr;
        }

        .cta-content h2 {
            font-size: 2rem;
        }
    }
</style>
