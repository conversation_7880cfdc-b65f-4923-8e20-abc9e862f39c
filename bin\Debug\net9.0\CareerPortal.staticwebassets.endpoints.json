{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "CareerPortal.modules.json", "AssetFile": "CareerPortal.modules.json.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.010638297872"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"EUcPo74MSmhv/hLMcB1hGxjI+dVgHeDs4j3s9WycJ0A=\""}, {"Name": "ETag", "Value": "W/\"sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig="}]}, {"Route": "CareerPortal.modules.json", "AssetFile": "CareerPortal.modules.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "114"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:15:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sgZOtYFoQbtT5NzEZj6mvNpqny3TPXduyD8nEg1wGig="}]}, {"Route": "CareerPortal.modules.json.gz", "AssetFile": "CareerPortal.modules.json.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "93"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"EUcPo74MSmhv/hLMcB1hGxjI+dVgHeDs4j3s9WycJ0A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EUcPo74MSmhv/hLMcB1hGxjI+dVgHeDs4j3s9WycJ0A="}]}, {"Route": "CareerPortal.styles.css", "AssetFile": "CareerPortal.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.009090909091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=\""}, {"Name": "ETag", "Value": "W/\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}]}, {"Route": "CareerPortal.styles.css", "AssetFile": "CareerPortal.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:15:44 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}]}, {"Route": "CareerPortal.styles.css.gz", "AssetFile": "CareerPortal.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM="}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css", "AssetFile": "CareerPortal.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.009090909091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=\""}, {"Name": "ETag", "Value": "W/\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}, {"Name": "label", "Value": "CareerPortal.styles.css"}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css", "AssetFile": "CareerPortal.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:15:44 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "integrity", "Value": "sha256-Cd9S5lUtr5lTGiHEJmHbRH4t2/2IEDvifB9ETIV+JAk="}, {"Name": "label", "Value": "CareerPortal.styles.css"}]}, {"Route": "CareerPortal.tnv30r1bl8.styles.css.gz", "AssetFile": "CareerPortal.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnv30r1bl8"}, {"Name": "integrity", "Value": "sha256-p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM="}, {"Name": "label", "Value": "CareerPortal.styles.css.gz"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003731343284"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"//clW2GxYKMKCQ11fp3GeXX3FIThT7MZFC8x35s69eU=\""}, {"Name": "ETag", "Value": "W/\"b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b9RSPukLvSHekr3kftcukF9Hbr4g1a5l0/cfyJ61XMA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Anchor/FluentAnchor.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"//clW2GxYKMKCQ11fp3GeXX3FIThT7MZFC8x35s69eU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-//clW2GxYKMKCQ11fp3GeXX3FIThT7MZFC8x35s69eU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000997008973"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1002"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Afys6mc7pO9MdyokTQwtIDBaLTcYTTR9veWNwNIktvU=\""}, {"Name": "ETag", "Value": "W/\"RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RC8P8iQcUu/cx7osATv0xSw2X8G3utkMtThwigsND5g="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1002"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Afys6mc7pO9MdyokTQwtIDBaLTcYTTR9veWNwNIktvU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Afys6mc7pO9MdyokTQwtIDBaLTcYTTR9veWNwNIktvU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "522"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003205128205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "311"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ILEdH4KtR1Hb7JiJVs56TK2orcZyV4zmIUMwLCiQMaE=\""}, {"Name": "ETag", "Value": "W/\"8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8QTQtCTbbHkwqt3rAy8ZPjez2lZ6PGmR5Il+7Q3g/rs="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Button/FluentButton.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "311"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ILEdH4KtR1Hb7JiJVs56TK2orcZyV4zmIUMwLCiQMaE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ILEdH4KtR1Hb7JiJVs56TK2orcZyV4zmIUMwLCiQMaE="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "368"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004587155963"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "217"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GN9Xra9rtwmLDCgEbgg9cmd8TqFT2hcKzoh25cmNDXU=\""}, {"Name": "ETag", "Value": "W/\"gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gVrV4WI8finQdUGG7EIZIAh2tTbFW0GF7Hl73l/1JnE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Checkbox/FluentCheckbox.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "217"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GN9Xra9rtwmLDCgEbgg9cmd8TqFT2hcKzoh25cmNDXU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GN9Xra9rtwmLDCgEbgg9cmd8TqFT2hcKzoh25cmNDXU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14173"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000337495781"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2962"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qW8M83Rbn9fAXv7nGXmChE7Qo2htZhG8ALruzktPsU8=\""}, {"Name": "ETag", "Value": "W/\"PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PbscDKK1vxqZeEv6a+4x9gKVhqxHVYgy9iE0fMp2iDY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DataGrid/FluentDataGrid.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2962"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qW8M83Rbn9fAXv7nGXmChE7Qo2htZhG8ALruzktPsU8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qW8M83Rbn9fAXv7nGXmChE7Qo2htZhG8ALruzktPsU8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2058"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001324503311"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "754"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m/2KN+FpPqIvh9wR4E9l87AbfY4hA5AtyFeDpfKxDbc=\""}, {"Name": "ETag", "Value": "W/\"abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-abaSp2xCB2qa0FHuc/9VKt39+MybEnIoOItPufNTSr4="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "754"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m/2KN+FpPqIvh9wR4E9l87AbfY4hA5AtyFeDpfKxDbc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m/2KN+FpPqIvh9wR4E9l87AbfY4hA5AtyFeDpfKxDbc="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "388"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004310344828"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5MY4SrjNix5pk9Q5gpyB70xS5vpChA2Ji/0LnyzJwjo=\""}, {"Name": "ETag", "Value": "W/\"CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CndcCP/YVXs68LoE68COc38ypIJenMbJyu+fR0/ZIPc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Divider/FluentDivider.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5MY4SrjNix5pk9Q5gpyB70xS5vpChA2Ji/0LnyzJwjo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5MY4SrjNix5pk9Q5gpyB70xS5vpChA2Ji/0LnyzJwjo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3005"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001315789474"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "759"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"i0j3kxAp406ufS3MsxTNvjzVgyRqDwIajgOehqvtu6M=\""}, {"Name": "ETag", "Value": "W/\"V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V4iZz/kay7SoC/eRuDViVZkhxiL1oNW1gzMAFC6k/wY="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Grid/FluentGrid.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "759"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"i0j3kxAp406ufS3MsxTNvjzVgyRqDwIajgOehqvtu6M=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-i0j3kxAp406ufS3MsxTNvjzVgyRqDwIajgOehqvtu6M="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "348"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007194244604"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "138"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hOhymz8Hwrl2LWr7Y0TXXQPcQmpfSCsFogOb6Zv2kB4=\""}, {"Name": "ETag", "Value": "W/\"yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yf+15AR63QV4X8XvrAMxrEP5sX3Ea0tuh+Tsinb6yXU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "138"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hOhymz8Hwrl2LWr7Y0TXXQPcQmpfSCsFogOb6Zv2kB4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hOhymz8Hwrl2LWr7Y0TXXQPcQmpfSCsFogOb6Zv2kB4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2813"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001197604790"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gbwptaJwa99wlBJATqA39Wvd+hPAzyivnXV9kSwBbNk=\""}, {"Name": "ETag", "Value": "W/\"m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m9D6O5smUPMQWbjax0bH03XYtdI3RD5geOwhizeT+gE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/InputFile/FluentInputFile.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gbwptaJwa99wlBJATqA39Wvd+hPAzyivnXV9kSwBbNk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gbwptaJwa99wlBJATqA39Wvd+hPAzyivnXV9kSwBbNk="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001121076233"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "891"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4VrmdaAeF/CVMJ7a55YiF2hEI0LDZi84KcPQcvi3ZM4=\""}, {"Name": "ETag", "Value": "W/\"3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3+jF/yOfwYyQhLujhQlSrvp3NBll+oEUF7v13pin53A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/KeyCode/FluentKeyCode.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "891"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4VrmdaAeF/CVMJ7a55YiF2hEI0LDZi84KcPQcvi3ZM4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4VrmdaAeF/CVMJ7a55YiF2hEI0LDZi84KcPQcvi3ZM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "473"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003968253968"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "251"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T9UdQlDeqtgxKoaOrC15cRB5b8Xw+a6l3AS6Y+XsXL8=\""}, {"Name": "ETag", "Value": "W/\"hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hXPNDHD1hTdz/sH1cD60f/ehIklf8zQAEE73UZNGtu8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Label/FluentInputLabel.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "251"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T9UdQlDeqtgxKoaOrC15cRB5b8Xw+a6l3AS6Y+XsXL8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T9UdQlDeqtgxKoaOrC15cRB5b8Xw+a6l3AS6Y+XsXL8="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1299"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001751313485"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "570"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gUtYatED8B/J0nKbZybxmXWnL/UJrKEPtENtEKPigwg=\""}, {"Name": "ETag", "Value": "W/\"2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2bhET+uXWbAao2aJyUqqscx9PObMTXmpUAkDQOQBGI8="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentAutocomplete.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "570"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gUtYatED8B/J0nKbZybxmXWnL/UJrKEPtENtEKPigwg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gUtYatED8B/J0nKbZybxmXWnL/UJrKEPtENtEKPigwg="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1483"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001834862385"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "544"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fhAJBRWOqIUuZBy3gnCpMXRnHWRrtx32KmxhrDCzqLQ=\""}, {"Name": "ETag", "Value": "W/\"OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OqLCO17dCq/aFFg8O0mXN/fF4czXAd6R+vgnYjtdPwc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/FluentCombobox.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "544"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fhAJBRWOqIUuZBy3gnCpMXRnHWRrtx32KmxhrDCzqLQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fhAJBRWOqIUuZBy3gnCpMXRnHWRrtx32KmxhrDCzqLQ="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007246376812"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "137"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uEs3SkTKioIAKwT/E2rYx8lRyA3csV0YWEB5VboYALI=\""}, {"Name": "ETag", "Value": "W/\"/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lFyXHGb/lh02BDFUuMzwbfU+zNOdnw2s2zKSrTtW00="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/List/ListComponentBase.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "137"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uEs3SkTKioIAKwT/E2rYx8lRyA3csV0YWEB5VboYALI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uEs3SkTKioIAKwT/E2rYx8lRyA3csV0YWEB5VboYALI="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "725"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003344481605"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aXVPltYYB1IqzLOOo/ihlIm1QLjjfas+mNS4YtuShCI=\""}, {"Name": "ETag", "Value": "W/\"C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C/YKywsVlWaSpZ1PLDeRKkkkM6ki2G2gT9ny+WVuERA="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Menu/FluentMenu.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aXVPltYYB1IqzLOOo/ihlIm1QLjjfas+mNS4YtuShCI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aXVPltYYB1IqzLOOo/ihlIm1QLjjfas+mNS4YtuShCI="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5345"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000987166831"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1012"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gI4r2CiVSDsh0E4W8EzzQkWVMP1z5KqWsHc5UHSf4lA=\""}, {"Name": "ETag", "Value": "W/\"u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u3HANg4jObqKg1Jso4ovjOp2lKuYeAN0+zlRIfKuHhw="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/NavMenu/FluentNavMenu.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1012"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gI4r2CiVSDsh0E4W8EzzQkWVMP1z5KqWsHc5UHSf4lA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gI4r2CiVSDsh0E4W8EzzQkWVMP1z5KqWsHc5UHSf4lA="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6575"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000569476082"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1755"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GLrvqsihiaOKSTpR4HUdwLbyE5ovO/PgdDHABPeQqQ4=\""}, {"Name": "ETag", "Value": "W/\"hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hVi+eZ1AhYzWA2HILBTSjl5xstub4DMGzUxGJIQgjVo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overflow/FluentOverflow.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1755"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GLrvqsihiaOKSTpR4HUdwLbyE5ovO/PgdDHABPeQqQ4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GLrvqsihiaOKSTpR4HUdwLbyE5ovO/PgdDHABPeQqQ4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001522070015"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "656"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XJbbN4OuwYXj7k13JHIv9Lj1t118BSzODet/C6GywE0=\""}, {"Name": "ETag", "Value": "W/\"IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IDySDi264SKaXFu1nL+hU2NeFhEMrX6Zv7ubUPR88VI="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Overlay/FluentOverlay.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "656"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XJbbN4OuwYXj7k13JHIv9Lj1t118BSzODet/C6GywE0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XJbbN4OuwYXj7k13JHIv9Lj1t118BSzODet/C6GywE0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6841"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000542005420"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1844"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ei4JgX2LTuVjcaPCISvNeX1hHKeJ8uYfuz0+kGtJAkI=\""}, {"Name": "ETag", "Value": "W/\"xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlA5fSAkA6TiFUznwHP835N8kAxJ7YJ5MTizYCGeOfo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1844"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ei4JgX2LTuVjcaPCISvNeX1hHKeJ8uYfuz0+kGtJAkI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ei4JgX2LTuVjcaPCISvNeX1hHKeJ8uYfuz0+kGtJAkI="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "917"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003048780488"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "327"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZDiIU0h55N8SSpI62bLHZqaEzTK3XYMZ7oWs+4kRUmo=\""}, {"Name": "ETag", "Value": "W/\"FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FpN8ZcuZyVhdYb+cHNB4VZ5bLM+yi3gDaTZbWsahaYE="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Search/FluentSearch.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "327"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZDiIU0h55N8SSpI62bLHZqaEzTK3XYMZ7oWs+4kRUmo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZDiIU0h55N8SSpI62bLHZqaEzTK3XYMZ7oWs+4kRUmo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "445"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003508771930"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ok3+UlpFQuzZXWBv0rF9abeEFcHU9G9/yGNlsDrEjXw=\""}, {"Name": "ETag", "Value": "W/\"TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TAnVg0aJviMtvE8pWYaaZahF5suJcjonGCC7accq76k="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSlider.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ok3+UlpFQuzZXWBv0rF9abeEFcHU9G9/yGNlsDrEjXw=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ok3+UlpFQuzZXWBv0rF9abeEFcHU9G9/yGNlsDrEjXw="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004545454545"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MEEF9qZp8vzu++12GS1Nrm9HsAwBidvWQdUYcJ+WJvY=\""}, {"Name": "ETag", "Value": "W/\"Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Em8bsrj69skLLR4IHVJ8lIJTR1EcY/U9nvcfn9t1rzo="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Slider/FluentSliderLabel.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "219"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MEEF9qZp8vzu++12GS1Nrm9HsAwBidvWQdUYcJ+WJvY=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MEEF9qZp8vzu++12GS1Nrm9HsAwBidvWQdUYcJ+WJvY="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1325"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002127659574"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "469"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2E4hhc6jOpgtINxrSRMKIqw+DQLQ2SftfcX1ThOrepM=\""}, {"Name": "ETag", "Value": "W/\"rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rBxLYd0QGHwfD9IZljh74Lf+ZC+zqoRLqwikRKcRgpg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/SortableList/FluentSortableList.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "469"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2E4hhc6jOpgtINxrSRMKIqw+DQLQ2SftfcX1ThOrepM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2E4hhc6jOpgtINxrSRMKIqw+DQLQ2SftfcX1ThOrepM="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6140"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000740740741"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1349"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1iL0Rcjo40DSIIzfew0csjsBypwBYzFxskd2cyvll1c=\""}, {"Name": "ETag", "Value": "W/\"kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kExJSsKpmByqtTJ/TOwptCU5yawR+13aqkZxoVN+a1A="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Splitter/FluentMultiSplitter.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1349"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1iL0Rcjo40DSIIzfew0csjsBypwBYzFxskd2cyvll1c=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1iL0Rcjo40DSIIzfew0csjsBypwBYzFxskd2cyvll1c="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "526"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003401360544"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "293"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7aHGbSJcSaT5cfjQK+Gyrtoyd3MYNywpo1mPbW862yk=\""}, {"Name": "ETag", "Value": "W/\"Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kh0YI9vhH0m+YJJvQVdOvtm0zuIIGEdRv3aH6iv7Gcg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tabs/FluentTab.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "293"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"7aHGbSJcSaT5cfjQK+Gyrtoyd3MYNywpo1mPbW862yk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7aHGbSJcSaT5cfjQK+Gyrtoyd3MYNywpo1mPbW862yk="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1187"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002150537634"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mCekEzA0zB8lJvQIv6M/2Ndz99hlmD0UE8rEDHgUAps=\""}, {"Name": "ETag", "Value": "W/\"YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YXiMRc9QPIiDSy+mlSF6DtYiSYb3X+1xlsCmrMrE2IU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/TextField/FluentTextField.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "464"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mCekEzA0zB8lJvQIv6M/2Ndz99hlmD0UE8rEDHgUAps=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mCekEzA0zB8lJvQIv6M/2Ndz99hlmD0UE8rEDHgUAps="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1364"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001945525292"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "513"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1OzW47M+BAzhQZXA66lSbRUQZP1ObnRRM4TTFKNDCh4=\""}, {"Name": "ETag", "Value": "W/\"s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s2w5uif33eV2OeQRoRzZYM1ANZXb6He68mkQ3IZw9Bc="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Toolbar/FluentToolbar.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "513"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1OzW47M+BAzhQZXA66lSbRUQZP1ObnRRM4TTFKNDCh4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1OzW47M+BAzhQZXA66lSbRUQZP1ObnRRM4TTFKNDCh4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "730"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003610108303"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8ePQzkTFcBOjLSt8PxALnbVMRW1l7AfeMHcGCtC1Jdg=\""}, {"Name": "ETag", "Value": "W/\"pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pWY0aUTl5SagZBQwX/+DOHxke3fHSPoZdTQXbRQSFTU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Components/Tooltip/FluentTooltip.razor.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8ePQzkTFcBOjLSt8PxALnbVMRW1l7AfeMHcGCtC1Jdg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8ePQzkTFcBOjLSt8PxALnbVMRW1l7AfeMHcGCtC1Jdg="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "101053"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071418369"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14001"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DUYRo3aBXcG5TcO2iCbSCYEify4c77QCT7R4VobttpI=\""}, {"Name": "ETag", "Value": "W/\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "392863"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "dependency-group", "Value": "js-initializer"}, {"Name": "integrity", "Value": "sha256-htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011079105"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "90259"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1X6i8A29INvrhpx2iST6R2pGeSEC9jwBm/t4w6I3TyA=\""}, {"Name": "ETag", "Value": "W/\"htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "dependency-group", "Value": "js-initializer"}, {"Name": "integrity", "Value": "sha256-htNuF2gbem5vP85zcYVih92CTJrIeZj4ghrN/AHd9VM="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1022"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001757469244"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"k1bvyfht4MPa+2CGIYyjCqpmHAmcIb02LT50Ty0KClk=\""}, {"Name": "ETag", "Value": "W/\"gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gD29yOMICDIiYM16Dl8m2EwS2lyds8DoFkgTy29qko4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"k1bvyfht4MPa+2CGIYyjCqpmHAmcIb02LT50Ty0KClk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k1bvyfht4MPa+2CGIYyjCqpmHAmcIb02LT50Ty0KClk="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "90259"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1X6i8A29INvrhpx2iST6R2pGeSEC9jwBm/t4w6I3TyA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1X6i8A29INvrhpx2iST6R2pGeSEC9jwBm/t4w6I3TyA="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1313081"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000003589105"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "278620"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"94gUrH5jYenrkpJAKcu8Rcc6G3T/PvRHDTVVsp6p+lo=\""}, {"Name": "ETag", "Value": "W/\"VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VHCb/U7GL6QVbx5psaGUdfmCzhTRPWRUuIbJLkEigYo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "278620"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"94gUrH5jYenrkpJAKcu8Rcc6G3T/PvRHDTVVsp6p+lo=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-94gUrH5jYenrkpJAKcu8Rcc6G3T/PvRHDTVVsp6p+lo="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "101053"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 13:05:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q21vm7bk8w"}, {"Name": "integrity", "Value": "sha256-zuaQLy76kNrlxjFX+6hX984arQH48y6Q/ghVBSpwWT0="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.bundle.scp.css"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14001"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DUYRo3aBXcG5TcO2iCbSCYEify4c77QCT7R4VobttpI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DUYRo3aBXcG5TcO2iCbSCYEify4c77QCT7R4VobttpI="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.1dlotxxwer.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7992"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1dlotxxwer"}, {"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7992"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000458295142"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2181"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Xl2r/uacHVD+mrIVAVDC5GAc5mMeiGjzQRAeyrzLwzc=\""}, {"Name": "ETag", "Value": "W/\"2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2wyFQ9++b6uYwv3gv265xtRV2OWnPQMN68NpUHffScU="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2181"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Xl2r/uacHVD+mrIVAVDC5GAc5mMeiGjzQRAeyrzLwzc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xl2r/uacHVD+mrIVAVDC5GAc5mMeiGjzQRAeyrzLwzc="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.f8c5bd5212.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f8c5bd5212"}, {"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js"}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001945525292"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "513"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"juVE/OQUgf6veO9EE5R7w8sUbTnBaMtqSwEftLHbeM4=\""}, {"Name": "ETag", "Value": "W/\"L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L9w4Nw5htE5XBWcy0I11eRfWwkTxtN8VSJWnitKu30Q="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/initializersLoader.webview.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "513"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"juVE/OQUgf6veO9EE5R7w8sUbTnBaMtqSwEftLHbeM4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-juVE/OQUgf6veO9EE5R7w8sUbTnBaMtqSwEftLHbeM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3190"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000845308538"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1182"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SzheGEdps0ArWi83idGsg6m6WlDMfdv4S5PpD4i+7pE=\""}, {"Name": "ETag", "Value": "W/\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js.gz", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1182"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SzheGEdps0ArWi83idGsg6m6WlDMfdv4S5PpD4i+7pE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SzheGEdps0ArWi83idGsg6m6WlDMfdv4S5PpD4i+7pE="}]}, {"Route": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.t5s4sbrbsi.js", "AssetFile": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3190"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Feb 2025 12:58:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t5s4sbrbsi"}, {"Name": "integrity", "Value": "sha256-kX+9ky61TMxar94Z7+S8myontpvgH4571DVehjxVvM4="}, {"Name": "label", "Value": "_content/Microsoft.FluentUI.AspNetCore.Components/js/loading-theme.js"}]}, {"Route": "app.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000428632662"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2332"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE=\""}, {"Name": "ETag", "Value": "W/\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5583"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}]}, {"Route": "app.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2332"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE="}]}, {"Route": "app.kwazt7t2v0.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000428632662"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2332"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE=\""}, {"Name": "ETag", "Value": "W/\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.kwazt7t2v0.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5583"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "integrity", "Value": "sha256-DNWEPHuAeFv+bQz2XfOzotB+XRMAcLSSaEbOq0sNt2A="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.kwazt7t2v0.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2332"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kwazt7t2v0"}, {"Name": "integrity", "Value": "sha256-ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE="}, {"Name": "label", "Value": "app.css.gz"}]}, {"Route": "css/landing.css", "AssetFile": "css/landing.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000464468184"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2152"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI=\""}, {"Name": "ETag", "Value": "W/\"GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc="}]}, {"Route": "css/landing.css", "AssetFile": "css/landing.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9772"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 10:01:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc="}]}, {"Route": "css/landing.css.gz", "AssetFile": "css/landing.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2152"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI="}]}, {"Route": "css/landing.ow8g543z8n.css", "AssetFile": "css/landing.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000464468184"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2152"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI=\""}, {"Name": "ETag", "Value": "W/\"GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ow8g543z8n"}, {"Name": "integrity", "Value": "sha256-GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc="}, {"Name": "label", "Value": "css/landing.css"}]}, {"Route": "css/landing.ow8g543z8n.css", "AssetFile": "css/landing.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9772"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 10:01:22 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ow8g543z8n"}, {"Name": "integrity", "Value": "sha256-GDaaZNHx580t5JZhj2S6kLw8S9JuUZkBiRGBFu5YLEc="}, {"Name": "label", "Value": "css/landing.css"}]}, {"Route": "css/landing.ow8g543z8n.css.gz", "AssetFile": "css/landing.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2152"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ow8g543z8n"}, {"Name": "integrity", "Value": "sha256-P5M2QVllYEiMt7IiC4+lrBlw/SwIW2cden+yYDyU9LI="}, {"Name": "label", "Value": "css/landing.css.gz"}]}, {"Route": "favicon.a8m5cweeeb.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000186636805"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5357"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=\""}, {"Name": "ETag", "Value": "W/\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.a8m5cweeeb.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.a8m5cweeeb.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5357"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a8m5cweeeb"}, {"Name": "integrity", "Value": "sha256-40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI="}, {"Name": "label", "Value": "favicon.ico.gz"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000186636805"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5357"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=\""}, {"Name": "ETag", "Value": "W/\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 05:34:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ymsgpgcP5QtFkpeuQQcIdyIQH79a9gyXGLH8FjqfURs="}]}, {"Route": "favicon.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5357"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 11:21:17 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI="}]}, {"Route": "images/logo.6sw81bt7n3.png", "AssetFile": "images/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3808"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CmtfNBcJ/NLjuatoucTNotcZb3PLhf4ZU7tPG2bsqco=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 31 Jan 2025 18:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6sw81bt7n3"}, {"Name": "integrity", "Value": "sha256-CmtfNBcJ/NLjuatoucTNotcZb3PLhf4ZU7tPG2bsqco="}, {"Name": "label", "Value": "images/logo.png"}]}, {"Route": "images/logo.png", "AssetFile": "images/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3808"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CmtfNBcJ/NLjuatoucTNotcZb3PLhf4ZU7tPG2bsqco=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 31 Jan 2025 18:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CmtfNBcJ/NLjuatoucTNotcZb3PLhf4ZU7tPG2bsqco="}]}]}