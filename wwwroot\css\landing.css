/* Pak Suzuki Career Portal Landing Page Styles */

/* Reset and base styles for landing page */
.landing-page * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

.landing-page {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    line-height: 1.6 !important;
    color: #333 !important;
    overflow-x: hidden !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Hero Section */
.landing-page .hero {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: white !important;
    padding: 80px 20px !important;
    text-align: center !important;
    position: relative !important;
    min-height: 500px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-direction: column !important;
    overflow: hidden !important;
    width: 100% !important;
    margin: 0 !important;
}

.landing-page .hero::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%) !important;
    z-index: 1 !important;
    animation: heroAnimation 20s ease-in-out infinite !important;
}

@keyframes heroAnimation {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.landing-page .hero-content {
    position: relative !important;
    z-index: 2 !important;
    max-width: 800px !important;
}

.landing-page .hero h1 {
    font-size: 3.5rem !important;
    font-weight: 700 !important;
    margin-bottom: 20px !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3) !important;
    color: white !important;
}

.landing-page .hero p {
    font-size: 1.3rem !important;
    margin-bottom: 30px !important;
    opacity: 0.95 !important;
    color: white !important;
}

.landing-page .cta-button {
    display: inline-block !important;
    background: #ff6b35 !important;
    color: white !important;
    padding: 15px 35px !important;
    text-decoration: none !important;
    border-radius: 50px !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3) !important;
}

.landing-page .cta-button:hover {
    background: #e55a2b !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4) !important;
    color: white !important;
}

/* Container */
.landing-page .container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 20px !important;
}

/* Section styles */
.landing-page .section {
    padding: 60px 0 !important;
    width: 100% !important;
}

.landing-page .section-title {
    text-align: center !important;
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    margin-bottom: 20px !important;
    color: #1e3c72 !important;
}

.landing-page .section-subtitle {
    text-align: center !important;
    font-size: 1.2rem !important;
    color: #666 !important;
    margin-bottom: 50px !important;
    max-width: 600px !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* Program sections */
.landing-page .program-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
    gap: 30px !important;
    margin-top: 40px !important;
}

.landing-page .program-card {
    background: white !important;
    border-radius: 15px !important;
    padding: 40px 30px !important;
    text-align: center !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
    border: 1px solid #f0f0f0 !important;
    opacity: 0 !important;
    transform: translateY(30px) !important;
    animation: fadeInUp 0.6s ease forwards !important;
}

.landing-page .program-card:nth-child(1) { animation-delay: 0.1s !important; }
.landing-page .program-card:nth-child(2) { animation-delay: 0.2s !important; }
.landing-page .program-card:nth-child(3) { animation-delay: 0.3s !important; }

.landing-page .program-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 40px rgba(0,0,0,0.15) !important;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.landing-page .program-icon {
    width: 80px !important;
    height: 80px !important;
    background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto 25px !important;
    font-size: 2rem !important;
    color: white !important;
}

.landing-page .program-card h3 {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    margin-bottom: 15px !important;
    color: #1e3c72 !important;
}

.landing-page .program-card p {
    color: #666 !important;
    margin-bottom: 25px !important;
    line-height: 1.6 !important;
}

.landing-page .learn-more {
    color: #1e3c72 !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    border-bottom: 2px solid transparent !important;
    transition: border-color 0.3s ease !important;
}

.landing-page .learn-more:hover {
    border-bottom-color: #1e3c72 !important;
    color: #1e3c72 !important;
}

/* Recent jobs section */
.jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 40px;
}

.job-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-left: 4px solid #ff6b35;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease forwards;
}

.job-card:nth-child(1) { animation-delay: 0.1s; }
.job-card:nth-child(2) { animation-delay: 0.2s; }
.job-card:nth-child(3) { animation-delay: 0.3s; }

.job-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.job-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3c72;
    margin-bottom: 10px;
}

.job-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

.job-description {
    color: #555;
    margin-bottom: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.apply-button {
    background: #1e3c72;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    display: inline-block;
    font-weight: 500;
    transition: background 0.3s ease;
}

.apply-button:hover {
    background: #2a5298;
}

/* Life at Pak Suzuki section */
.life-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-item {
    text-align: center;
    padding: 20px;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: #ff6b35;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 1.5rem;
}

.feature-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #1e3c72;
}

.feature-item p {
    color: #666;
    font-size: 0.95rem;
}

/* Footer */
.footer {
    background: #1e3c72;
    color: white;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #ff6b35;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #2a5298;
    padding-top: 20px;
    text-align: center;
    color: #ccc;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero {
        padding: 60px 15px;
        min-height: 400px;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero p {
        font-size: 1.1rem;
    }
    
    .section {
        padding: 40px 0;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .program-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .program-card {
        padding: 30px 20px;
    }
    
    .jobs-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .job-meta {
        flex-direction: column;
        gap: 5px;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .program-card {
        padding: 25px 15px;
    }
    
    .job-card {
        padding: 20px;
    }
}
