@using CareerPortal.Data.DTOs
@using System.ComponentModel.DataAnnotations

<div class="tab-panel">
    <h3>Personal Information</h3>
    <p class="section-description">Update your basic personal and contact information</p>

    <EditForm Model="editModel" OnValidSubmit="HandleValidSubmit" class="profile-form">
        <DataAnnotationsValidator />
        
        <div class="form-grid">
            <!-- Name Section -->
            <div class="form-section">
                <h4 class="section-title">Name</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName" class="form-label">First Name *</label>
                        <InputText id="firstName" @bind-Value="editModel.FirstName" class="form-input" placeholder="Enter your first name" />
                        <ValidationMessage For="() => editModel.FirstName" class="validation-message" />
                    </div>
                    <div class="form-group">
                        <label for="lastName" class="form-label">Last Name *</label>
                        <InputText id="lastName" @bind-Value="editModel.LastName" class="form-input" placeholder="Enter your last name" />
                        <ValidationMessage For="() => editModel.LastName" class="validation-message" />
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="form-section">
                <h4 class="section-title">Contact Information</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="mobileNumber" class="form-label">Mobile Number</label>
                        <InputText id="mobileNumber" @bind-Value="editModel.MobileNumber" class="form-input" placeholder="+92-300-1234567" />
                        <ValidationMessage For="() => editModel.MobileNumber" class="validation-message" />
                    </div>
                    <div class="form-group">
                        <label for="phoneNumber" class="form-label">Phone Number</label>
                        <InputText id="phoneNumber" @bind-Value="editModel.PhoneNumber" class="form-input" placeholder="+92-21-1234567" />
                        <ValidationMessage For="() => editModel.PhoneNumber" class="validation-message" />
                    </div>
                </div>
                <div class="form-group">
                    <label for="address" class="form-label">Address</label>
                    <InputTextArea id="address" @bind-Value="editModel.Address" class="form-textarea" placeholder="Enter your complete address" rows="3" />
                    <ValidationMessage For="() => editModel.Address" class="validation-message" />
                </div>
            </div>

            <!-- Identity Documents -->
            <div class="form-section">
                <h4 class="section-title">Identity Documents</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="cnic" class="form-label">CNIC</label>
                        <InputText id="cnic" @bind-Value="editModel.CNIC" class="form-input" placeholder="12345-1234567-1" />
                        <ValidationMessage For="() => editModel.CNIC" class="validation-message" />
                    </div>
                    <div class="form-group">
                        <label for="passportNumber" class="form-label">Passport Number</label>
                        <InputText id="passportNumber" @bind-Value="editModel.PassportNumber" class="form-input" placeholder="*********" />
                        <ValidationMessage For="() => editModel.PassportNumber" class="validation-message" />
                    </div>
                </div>
            </div>

            <!-- Professional Links -->
            <div class="form-section">
                <h4 class="section-title">Professional Links</h4>
                <div class="form-group">
                    <label for="linkedInUrl" class="form-label">LinkedIn Profile URL</label>
                    <InputText id="linkedInUrl" @bind-Value="editModel.LinkedInProfileUrl" class="form-input" placeholder="https://www.linkedin.com/in/yourprofile" />
                    <ValidationMessage For="() => editModel.LinkedInProfileUrl" class="validation-message" />
                </div>
            </div>

            <!-- Profile Summary -->
            <div class="form-section">
                <h4 class="section-title">Professional Summary</h4>
                <div class="form-group">
                    <label for="profileSummary" class="form-label">Summary</label>
                    <InputTextArea id="profileSummary" @bind-Value="editModel.ProfileSummary" class="form-textarea" 
                                   placeholder="Write a brief professional summary highlighting your key skills and experience..." rows="5" />
                    <ValidationMessage For="() => editModel.ProfileSummary" class="validation-message" />
                    <small class="form-help">This summary will be visible to potential employers</small>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                @if (isSubmitting)
                {
                    <span class="loading-spinner"></span>
                    <span>Saving...</span>
                }
                else
                {
                    <span>Save Changes</span>
                }
            </button>
            <button type="button" class="btn btn-secondary" @onclick="ResetForm">
                Reset
            </button>
        </div>
    </EditForm>
</div>

<style>
    .section-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 1rem;
    }

    .profile-form {
        max-width: 800px;
    }

    .form-grid {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .form-section {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .section-title {
        color: #1e3c72;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        border-bottom: 2px solid #e3f2fd;
        padding-bottom: 8px;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .form-label {
        font-weight: 500;
        color: #333;
        font-size: 0.95rem;
    }

    .form-input,
    .form-textarea {
        padding: 12px 16px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 1rem;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
        background: white;
    }

    .form-input:focus,
    .form-textarea:focus {
        outline: none;
        border-color: #1e3c72;
        box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
    }

    .form-textarea {
        resize: vertical;
        min-height: 80px;
    }

    .form-help {
        color: #666;
        font-size: 0.85rem;
        font-style: italic;
    }

    .validation-message {
        color: #dc3545;
        font-size: 0.85rem;
        margin-top: 4px;
    }

    .form-actions {
        display: flex;
        gap: 15px;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;
        justify-content: center;
    }

    .btn-primary {
        background-color: #1e3c72;
        color: white;
    }

    .btn-primary:hover:not(:disabled) {
        background-color: #2a5298;
        transform: translateY(-1px);
    }

    .btn-primary:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        transform: translateY(-1px);
    }

    .loading-spinner {
        width: 16px;
        height: 16px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .form-section {
            padding: 20px;
        }

        .form-actions {
            flex-direction: column;
        }

        .btn {
            width: 100%;
        }
    }

    @@media (max-width: 480px) {
        .form-section {
            padding: 15px;
        }

        .section-title {
            font-size: 1.1rem;
        }

        .form-input,
        .form-textarea {
            padding: 10px 12px;
        }
    }
</style>

@code {
    [Parameter] public PersonalInfoDto PersonalInfo { get; set; } = new();
    [Parameter] public EventCallback<PersonalInfoDto> OnSave { get; set; }

    private PersonalInfoDto editModel = new();
    private bool isSubmitting = false;

    protected override void OnParametersSet()
    {
        // Create a copy of the PersonalInfo for editing
        editModel = new PersonalInfoDto
        {
            FirstName = PersonalInfo.FirstName,
            LastName = PersonalInfo.LastName,
            MobileNumber = PersonalInfo.MobileNumber,
            PhoneNumber = PersonalInfo.PhoneNumber,
            Address = PersonalInfo.Address,
            CNIC = PersonalInfo.CNIC,
            PassportNumber = PersonalInfo.PassportNumber,
            LinkedInProfileUrl = PersonalInfo.LinkedInProfileUrl,
            ProfileSummary = PersonalInfo.ProfileSummary
        };
    }

    private async Task HandleValidSubmit()
    {
        isSubmitting = true;
        try
        {
            await OnSave.InvokeAsync(editModel);
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void ResetForm()
    {
        OnParametersSet(); // Reset to original values
    }
}
