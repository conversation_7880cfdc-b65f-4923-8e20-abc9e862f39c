@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Identity
@using CareerPortal.Data
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager Navigation
@inject SignInManager<ApplicationUser> SignInManager

<nav class="global-nav">
    <div class="nav-container">
        <!-- Logo Section -->
        <div class="nav-logo">
            <a href="/" class="logo-link">
                <img src="/images/logo.png" alt="Company Logo" class="logo-image" />
            </a>
        </div>

        <!-- Navigation Links -->
        <div class="nav-links">
            <a href="/" class="nav-link">Home</a>
            <a href="/jobs" class="nav-link">Jobs</a>
        </div>

        <!-- Authentication Section -->
        <div class="nav-auth">
            <AuthorizeView>
                <Authorized>
                    <div class="user-menu">
                        <button class="user-button" @onclick="ToggleUserMenu">
                            <span class="user-name">@GetUserDisplayName(context.User)</span>
                            <span class="dropdown-arrow">▼</span>
                        </button>
                        
                        @if (showUserMenu)
                        {
                            <div class="user-dropdown">
                                <a href="/profile" class="dropdown-item">
                                    <span class="dropdown-icon">👤</span>
                                    Manage Profile
                                </a>
                                <a href="/job-preferences" class="dropdown-item">
                                    <span class="dropdown-icon">🔔</span>
                                    Job Preferences
                                </a>
                                <hr class="dropdown-divider" />
                                <form action="/Account/Logout" method="post" class="logout-form">
                                    <AntiforgeryToken />
                                    <input type="hidden" name="ReturnUrl" value="@currentUrl" />
                                    <button type="submit" class="dropdown-item logout-button">
                                        <span class="dropdown-icon">🚪</span>
                                        Logout
                                    </button>
                                </form>
                            </div>
                        }
                    </div>
                </Authorized>
                <NotAuthorized>
                    <div class="auth-buttons">
                        <a href="/Account/Login" class="auth-button login-button">Login</a>
                        <a href="/Account/Register" class="auth-button signup-button">Sign Up</a>
                    </div>
                </NotAuthorized>
            </AuthorizeView>
        </div>

        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle" @onclick="ToggleMobileMenu">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
        </button>
    </div>

    <!-- Mobile Menu -->
    @if (showMobileMenu)
    {
        <div class="mobile-menu">
            <div class="mobile-nav-links">
                <a href="/" class="mobile-nav-link">Home</a>
                <a href="/jobs" class="mobile-nav-link">Jobs</a>
            </div>
            
            <div class="mobile-auth">
                <AuthorizeView>
                    <Authorized>
                        <div class="mobile-user-section">
                            <span class="mobile-user-name">@GetUserDisplayName(context.User)</span>
                            <a href="/profile" class="mobile-nav-link">Manage Profile</a>
                            <a href="/job-preferences" class="mobile-nav-link">Job Preferences</a>
                            <form action="/Account/Logout" method="post" class="mobile-logout-form">
                                <AntiforgeryToken />
                                <input type="hidden" name="ReturnUrl" value="@currentUrl" />
                                <button type="submit" class="mobile-logout-button">Logout</button>
                            </form>
                        </div>
                    </Authorized>
                    <NotAuthorized>
                        <div class="mobile-auth-buttons">
                            <a href="/Account/Login" class="mobile-auth-button">Login</a>
                            <a href="/Account/Register" class="mobile-auth-button">Sign Up</a>
                        </div>
                    </NotAuthorized>
                </AuthorizeView>
            </div>
        </div>
    }
</nav>

<style>
    .global-nav {
        background: #fff;
        border-bottom: 1px solid #e0e0e0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: sticky;
        top: 0;
        z-index: 1000;
    }

    .nav-container {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        height: 70px;
    }

    .nav-logo .logo-link {
        display: flex;
        align-items: center;
        text-decoration: none;
    }

    .logo-image {
        height: 50px;
        width: auto;
    }

    .nav-links {
        display: flex;
        gap: 30px;
        align-items: center;
    }

    .nav-link {
        text-decoration: none;
        color: #333;
        font-weight: 500;
        padding: 8px 16px;
        border-radius: 4px;
        transition: background-color 0.3s ease;
    }

    .nav-link:hover {
        background-color: #f5f5f5;
        color: #1e3c72;
    }

    .nav-auth {
        position: relative;
    }

    .user-menu {
        position: relative;
    }

    .user-button {
        background: none;
        border: none;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .user-button:hover {
        background-color: #f5f5f5;
    }

    .user-name {
        font-weight: 500;
        color: #333;
    }

    .dropdown-arrow {
        font-size: 12px;
        color: #666;
    }

    .user-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        min-width: 200px;
        z-index: 1001;
        margin-top: 4px;
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        text-decoration: none;
        color: #333;
        transition: background-color 0.3s ease;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        cursor: pointer;
    }

    .dropdown-item:hover {
        background-color: #f5f5f5;
    }

    .dropdown-icon {
        font-size: 16px;
    }

    .dropdown-divider {
        margin: 8px 0;
        border: none;
        border-top: 1px solid #e0e0e0;
    }

    .logout-form {
        margin: 0;
    }

    .logout-button {
        color: #dc3545;
    }

    .logout-button:hover {
        background-color: #fff5f5;
    }

    .auth-buttons {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .auth-button {
        text-decoration: none;
        padding: 8px 20px;
        border-radius: 4px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .login-button {
        color: #1e3c72;
        border: 1px solid #1e3c72;
    }

    .login-button:hover {
        background-color: #1e3c72;
        color: white;
    }

    .signup-button {
        background-color: #1e3c72;
        color: white;
        border: 1px solid #1e3c72;
    }

    .signup-button:hover {
        background-color: #2a5298;
    }

    .mobile-menu-toggle {
        display: none;
        flex-direction: column;
        background: none;
        border: none;
        cursor: pointer;
        padding: 8px;
    }

    .hamburger-line {
        width: 25px;
        height: 3px;
        background-color: #333;
        margin: 3px 0;
        transition: 0.3s;
    }

    .mobile-menu {
        display: none;
        background: white;
        border-top: 1px solid #e0e0e0;
        padding: 20px;
    }

    .mobile-nav-links {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 20px;
    }

    .mobile-nav-link {
        text-decoration: none;
        color: #333;
        font-weight: 500;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .mobile-user-section {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .mobile-user-name {
        font-weight: 600;
        color: #1e3c72;
        padding: 8px 0;
    }

    .mobile-logout-form {
        margin-top: 12px;
    }

    .mobile-logout-button {
        background: none;
        border: none;
        color: #dc3545;
        font-weight: 500;
        padding: 12px 0;
        cursor: pointer;
        width: 100%;
        text-align: left;
    }

    .mobile-auth-buttons {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .mobile-auth-button {
        text-decoration: none;
        padding: 12px 20px;
        border-radius: 4px;
        font-weight: 500;
        text-align: center;
        border: 1px solid #1e3c72;
        color: #1e3c72;
    }

    .mobile-auth-button:first-child {
        background-color: #1e3c72;
        color: white;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .nav-links {
            display: none;
        }

        .auth-buttons {
            display: none;
        }

        .mobile-menu-toggle {
            display: flex;
        }

        .mobile-menu {
            display: block;
        }

        .nav-container {
            padding: 0 15px;
        }

        .logo-image {
            height: 40px;
        }
    }
</style>

@code {
    private bool showUserMenu = false;
    private bool showMobileMenu = false;
    private string currentUrl = "";

    protected override void OnInitialized()
    {
        currentUrl = Navigation.ToBaseRelativePath(Navigation.Uri);
    }

    private void ToggleUserMenu()
    {
        showUserMenu = !showUserMenu;
    }

    private void ToggleMobileMenu()
    {
        showMobileMenu = !showMobileMenu;
    }

    private string GetUserDisplayName(System.Security.Claims.ClaimsPrincipal user)
    {
        return user.Identity?.Name ?? "User";
    }
}
