@page "/profile"
@layout CareerPortal.Components.Layout.CareerPortalLayout
@using CareerPortal.Data.DTOs
@using CareerPortal.Services
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Authorization
@using CareerPortal.Data
@using CareerPortal.Components.Shared
@attribute [Authorize]
@inject IProfileService ProfileService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserManager<ApplicationUser> UserManager

<PageTitle>Manage Profile - Career Portal</PageTitle>

<div class="profile-page">
    <div class="profile-container">
        <div class="page-header">
            <h1 class="page-title">Manage Your Profile</h1>
            <p class="page-subtitle">Keep your professional information up to date</p>
        </div>

        <div class="profile-tabs">
            <button class="tab-button @(activeTab == "personal" ? "active" : "")" 
                    
                    @onclick="@(() => SetActiveTab("personal"))">
                Personal Information
            </button>
            <button class="tab-button @(activeTab == "experience" ? "active" : "")" 
                    @onclick="@(() => SetActiveTab("experience"))">
                Work Experience
            </button>
            <button class="tab-button @(activeTab == "education" ? "active" : "")" 
                    @onclick="@(() => SetActiveTab("education"))">
                Education
            </button>
            <button class="tab-button @(activeTab == "skills" ? "active" : "")" 
                    @onclick="@(() => SetActiveTab("skills"))">
                Skills
            </button>
            <button class="tab-button @(activeTab == "certifications" ? "active" : "")" 
                    
                    @onclick="@(() => SetActiveTab("certifications"))">
                Certifications
            </button>
            <button class="tab-button @(activeTab == "achievements" ? "active" : "")" 
                    
                    @onclick="@(() => SetActiveTab("achievements"))">
                Achievements
            </button>
        </div>

        <div class="tab-content">
            @if (activeTab == "personal")
            {
                <PersonalInfoTab PersonalInfo="personalInfo" OnSave="SavePersonalInfo" />
            }
            else if (activeTab == "experience")
            {
                <ExperienceTab Experiences="experiences" OnAdd="AddExperience" OnEdit="EditExperience" OnDelete="DeleteExperience" />
            }
            else if (activeTab == "education")
            {
                <EducationTab Educations="educations" OnAdd="AddEducation" OnEdit="EditEducation" OnDelete="DeleteEducation" />
            }
            else if (activeTab == "skills")
            {
                <div class="tab-panel">
                    <h3>Skills Management</h3>
                    <p>Skills management functionality coming soon...</p>
                </div>
            }
            else if (activeTab == "certifications")
            {
                <div class="tab-panel">
                    <h3>Training & Certifications</h3>
                    <p>Certifications management functionality coming soon...</p>
                </div>
            }
            else if (activeTab == "achievements")
            {
                <div class="tab-panel">
                    <h3>Achievements & Awards</h3>
                    <p>Achievements management functionality coming soon...</p>
                </div>
            }
        </div>
    </div>
</div>

@if (showSuccessMessage)
{
    <div class="success-toast">
        <span class="toast-icon">✅</span>
        <span>@successMessage</span>
    </div>
}

@if (showErrorMessage)
{
    <div class="error-toast">
        <span class="toast-icon">❌</span>
        <span>@errorMessage</span>
    </div>
}

<style>
    .profile-page {
        background-color: #f8f9fa;
        min-height: calc(100vh - 70px);
        padding: 40px 0;
    }

    .profile-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .page-header {
        text-align: center;
        margin-bottom: 40px;
    }

    .page-title {
        font-size: 2.5rem;
        color: #1e3c72;
        margin-bottom: 10px;
        font-weight: 700;
    }

    .page-subtitle {
        font-size: 1.1rem;
        color: #666;
        max-width: 600px;
        margin: 0 auto;
    }

    .profile-tabs {
        display: flex;
        background: white;
        border-radius: 12px 12px 0 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow-x: auto;
        margin-bottom: 0;
    }

    .tab-button {
        background: none;
        border: none;
        padding: 20px 25px;
        cursor: pointer;
        font-size: 1rem;
        font-weight: 500;
        color: #666;
        transition: all 0.3s ease;
        white-space: nowrap;
        border-bottom: 3px solid transparent;
    }

    .tab-button:hover {
        background-color: #f8f9fa;
        color: #1e3c72;
    }

    .tab-button.active {
        color: #1e3c72;
        border-bottom-color: #1e3c72;
        background-color: #f8f9fa;
    }

    .tab-content {
        background: white;
        border-radius: 0 0 12px 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        min-height: 500px;
    }

    .tab-panel {
        padding: 40px;
    }

    .tab-panel h3 {
        color: #1e3c72;
        margin-bottom: 20px;
        font-size: 1.5rem;
    }

    .success-toast,
    .error-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 10px;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    }

    .success-toast {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .error-toast {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    @@keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .profile-container {
            padding: 0 15px;
        }

        .page-title {
            font-size: 2rem;
        }

        .profile-tabs {
            border-radius: 8px 8px 0 0;
        }

        .tab-content {
            border-radius: 0 0 8px 8px;
        }

        .tab-button {
            padding: 15px 20px;
            font-size: 0.9rem;
        }

        .tab-panel {
            padding: 25px;
        }

        .success-toast,
        .error-toast {
            right: 15px;
            left: 15px;
            top: 15px;
        }
    }

    @@media (max-width: 480px) {
        .profile-page {
            padding: 20px 0;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .tab-button {
            padding: 12px 15px;
            font-size: 0.85rem;
        }

        .tab-panel {
            padding: 20px;
        }
    }
</style>

@code {
    private string activeTab = "personal";
    private string currentUserId = "";
    
    private PersonalInfoDto personalInfo = new();
    private List<ExperienceDto> experiences = new();
    private List<EducationDto> educations = new();
    
    private bool showSuccessMessage = false;
    private bool showErrorMessage = false;
    private string successMessage = "";
    private string errorMessage = "";

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = await UserManager.GetUserAsync(authState.User);
        
        if (user != null)
        {
            currentUserId = user.Id;
            await LoadProfileData();
        }
    }

    private async Task LoadProfileData()
    {
        try
        {
            personalInfo = await ProfileService.GetPersonalInfoAsync(currentUserId) ?? new PersonalInfoDto();
            experiences = await ProfileService.GetExperiencesAsync(currentUserId);
            educations = await ProfileService.GetEducationsAsync(currentUserId);
        }
        catch (Exception ex)
        {
            await ShowError("Failed to load profile data.");
        }
    }

    private void SetActiveTab(string tab)
    {
        activeTab = tab;
    }

    private async Task SavePersonalInfo(PersonalInfoDto dto)
    {
        var success = await ProfileService.UpdatePersonalInfoAsync(currentUserId, dto);
        if (success)
        {
            personalInfo = dto;
            await ShowSuccess("Personal information updated successfully!");
        }
        else
        {
            await ShowError("Failed to update personal information.");
        }
    }

    private async Task AddExperience(ExperienceDto dto)
    {
        var success = await ProfileService.AddExperienceAsync(currentUserId, dto);
        if (success)
        {
            experiences = await ProfileService.GetExperiencesAsync(currentUserId);
            await ShowSuccess("Work experience added successfully!");
        }
        else
        {
            await ShowError("Failed to add work experience.");
        }
    }

    private async Task EditExperience(ExperienceDto dto)
    {
        var success = await ProfileService.UpdateExperienceAsync(currentUserId, dto);
        if (success)
        {
            experiences = await ProfileService.GetExperiencesAsync(currentUserId);
            await ShowSuccess("Work experience updated successfully!");
        }
        else
        {
            await ShowError("Failed to update work experience.");
        }
    }

    private async Task DeleteExperience(int experienceId)
    {
        var success = await ProfileService.DeleteExperienceAsync(currentUserId, experienceId);
        if (success)
        {
            experiences = await ProfileService.GetExperiencesAsync(currentUserId);
            await ShowSuccess("Work experience deleted successfully!");
        }
        else
        {
            await ShowError("Failed to delete work experience.");
        }
    }

    private async Task AddEducation(EducationDto dto)
    {
        var success = await ProfileService.AddEducationAsync(currentUserId, dto);
        if (success)
        {
            educations = await ProfileService.GetEducationsAsync(currentUserId);
            await ShowSuccess("Education added successfully!");
        }
        else
        {
            await ShowError("Failed to add education.");
        }
    }

    private async Task EditEducation(EducationDto dto)
    {
        var success = await ProfileService.UpdateEducationAsync(currentUserId, dto);
        if (success)
        {
            educations = await ProfileService.GetEducationsAsync(currentUserId);
            await ShowSuccess("Education updated successfully!");
        }
        else
        {
            await ShowError("Failed to update education.");
        }
    }

    private async Task DeleteEducation(int educationId)
    {
        var success = await ProfileService.DeleteEducationAsync(currentUserId, educationId);
        if (success)
        {
            educations = await ProfileService.GetEducationsAsync(currentUserId);
            await ShowSuccess("Education deleted successfully!");
        }
        else
        {
            await ShowError("Failed to delete education.");
        }
    }

    private async Task ShowSuccess(string message)
    {
        successMessage = message;
        showSuccessMessage = true;
        StateHasChanged();
        await Task.Delay(3000);
        showSuccessMessage = false;
        StateHasChanged();
    }

    private async Task ShowError(string message)
    {
        errorMessage = message;
        showErrorMessage = true;
        StateHasChanged();
        await Task.Delay(3000);
        showErrorMessage = false;
        StateHasChanged();
    }
}