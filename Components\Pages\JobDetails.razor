@page "/job/{JobId:int}"
@layout CareerPortal.Components.Layout.CareerPortalLayout
@using CareerPortal.Data
@using Microsoft.EntityFrameworkCore
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Identity
@inject ApplicationDbContext DbContext
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserManager<ApplicationUser> UserManager

<PageTitle>@(job?.Title ?? "Job Details") - Career Portal</PageTitle>

<div class="job-details-page">
    @if (job != null)
    {
        <div class="job-details-container">
            <!-- Breadcrumb -->
            <nav class="breadcrumb">
                <a href="/jobs" class="breadcrumb-link">← Back to Jobs</a>
            </nav>

            <!-- Job Header -->
            <div class="job-header">
                <div class="job-header-content">
                    <h1 class="job-title">@job.Title</h1>
                    <div class="job-meta">
                        <span class="job-department">@job.Department</span>
                        <div class="job-location">
                            <span class="meta-icon">📍</span>
                            @job.Location
                        </div>
                        <div class="job-posted">
                            <span class="meta-icon">📅</span>
                            Posted @GetRelativeTime(job.PostedDate)
                        </div>
                        @if (job.ClosingDate.HasValue)
                        {
                            <div class="job-closing">
                                <span class="meta-icon">⏰</span>
                                Closes: @job.ClosingDate.Value.ToString("MMM dd, yyyy")
                            </div>
                        }
                    </div>
                </div>
                
                <!-- Apply Button Section -->
                <div class="apply-section">
                    <AuthorizeView>
                        <Authorized>
                            @if (hasApplied)
                            {
                                <div class="applied-status">
                                    <span class="applied-icon">✅</span>
                                    <span>Application Submitted</span>
                                </div>
                            }
                            else
                            {
                                <button class="apply-button" @onclick="ApplyForJob" disabled="@isApplying">
                                    @if (isApplying)
                                    {
                                        <span class="loading-spinner-small"></span>
                                        <span>Applying...</span>
                                    }
                                    else
                                    {
                                        <span>Apply Now</span>
                                    }
                                </button>
                            }
                        </Authorized>
                        <NotAuthorized>
                            <div class="login-prompt">
                                <p class="login-message">Please log in to apply for this position</p>
                                <div class="auth-buttons">
                                    <a href="/Account/Login?returnUrl=@Uri.EscapeDataString(Navigation.Uri)" class="login-button">Login</a>
                                    <a href="/Account/Register?returnUrl=@Uri.EscapeDataString(Navigation.Uri)" class="signup-button">Sign Up</a>
                                </div>
                            </div>
                        </NotAuthorized>
                    </AuthorizeView>
                </div>
            </div>

            <!-- Job Content -->
            <div class="job-content">
                <div class="job-main">
                    <!-- Job Description -->
                    <section class="job-section">
                        <h2 class="section-title">Job Description</h2>
                        <div class="section-content">
                            @((MarkupString)FormatText(job.Description))
                        </div>
                    </section>

                    <!-- Responsibilities -->
                    <section class="job-section">
                        <h2 class="section-title">Key Responsibilities</h2>
                        <div class="section-content">
                            @((MarkupString)FormatText(job.Responsibilities))
                        </div>
                    </section>

                    <!-- Qualifications -->
                    <section class="job-section">
                        <h2 class="section-title">Required Qualifications</h2>
                        <div class="section-content">
                            @((MarkupString)FormatText(job.Qualifications))
                        </div>
                    </section>
                </div>

                <!-- Sidebar -->
                <div class="job-sidebar">
                    <div class="sidebar-card">
                        <h3 class="sidebar-title">Job Information</h3>
                        <div class="job-info-list">
                            <div class="info-item">
                                <span class="info-label">Department:</span>
                                <span class="info-value">@job.Department</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Location:</span>
                                <span class="info-value">@job.Location</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Posted:</span>
                                <span class="info-value">@job.PostedDate.ToString("MMM dd, yyyy")</span>
                            </div>
                            @if (job.ClosingDate.HasValue)
                            {
                                <div class="info-item">
                                    <span class="info-label">Application Deadline:</span>
                                    <span class="info-value">@job.ClosingDate.Value.ToString("MMM dd, yyyy")</span>
                                </div>
                            }
                        </div>
                    </div>

                    <div class="sidebar-card">
                        <h3 class="sidebar-title">Share This Job</h3>
                        <div class="share-buttons">
                            <button class="share-button" @onclick="@(() => ShareJob("linkedin"))">
    LinkedIn
</button>
<button class="share-button" @onclick="@(() => ShareJob("email"))">
    Email
</button>
<button class="share-button" @onclick="CopyJobLink">
    Copy Link
</button>
                        </div>
                    </div>

                    <div class="sidebar-card">
                        <h3 class="sidebar-title">Similar Jobs</h3>
                        <div class="similar-jobs">
                            @if (similarJobs?.Any() == true)
                            {
                                @foreach (var similarJob in similarJobs.Take(3))
                                {
                                    <div class="similar-job-item" @onclick="() => NavigateToJob(similarJob.Id)">
                                        <h4 class="similar-job-title">@similarJob.Title</h4>
                                        <p class="similar-job-meta">@similarJob.Department • @similarJob.Location</p>
                                    </div>
                                }
                            }
                            else
                            {
                                <p class="no-similar-jobs">No similar jobs found</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else if (isLoading)
    {
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading job details...</p>
        </div>
    }
    else
    {
        <div class="error-container">
            <div class="error-icon">❌</div>
            <h2>Job Not Found</h2>
            <p>The job you're looking for doesn't exist or has been removed.</p>
            <a href="/jobs" class="back-to-jobs-button">Browse All Jobs</a>
        </div>
    }
</div>

@if (showSuccessMessage)
{
    <div class="success-toast">
        <span class="toast-icon">✅</span>
        <span>Application submitted successfully!</span>
    </div>
}

@if (showErrorMessage)
{
    <div class="error-toast">
        <span class="toast-icon">❌</span>
        <span>@errorMessage</span>
    </div>
}

<style>
    .job-details-page {
        background-color: #f8f9fa;
        min-height: calc(100vh - 70px);
        padding: 20px 0;
    }

    .job-details-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .breadcrumb {
        margin-bottom: 30px;
    }

    .breadcrumb-link {
        color: #1e3c72;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .breadcrumb-link:hover {
        color: #2a5298;
    }

    .job-header {
        background: white;
        border-radius: 12px;
        padding: 40px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 40px;
    }

    .job-header-content {
        flex: 1;
    }

    .job-title {
        font-size: 2.5rem;
        color: #1e3c72;
        margin-bottom: 20px;
        font-weight: 700;
        line-height: 1.2;
    }

    .job-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        align-items: center;
    }

    .job-department {
        background-color: #e3f2fd;
        color: #1e3c72;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .job-location,
    .job-posted,
    .job-closing {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #666;
        font-size: 0.95rem;
    }

    .meta-icon {
        font-size: 1rem;
    }

    .apply-section {
        flex-shrink: 0;
    }

    .apply-button {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
        min-width: 150px;
        justify-content: center;
    }

    .apply-button:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
    }

    .apply-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    .applied-status {
        display: flex;
        align-items: center;
        gap: 10px;
        color: #28a745;
        font-weight: 600;
        font-size: 1.1rem;
        padding: 15px 20px;
        background-color: #d4edda;
        border-radius: 8px;
        border: 1px solid #c3e6cb;
    }

    .applied-icon {
        font-size: 1.2rem;
    }

    .login-prompt {
        text-align: center;
        padding: 20px;
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
    }

    .login-message {
        margin-bottom: 15px;
        color: #856404;
        font-weight: 500;
    }

    .auth-buttons {
        display: flex;
        gap: 10px;
        justify-content: center;
    }

    .login-button,
    .signup-button {
        text-decoration: none;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .login-button {
        color: #1e3c72;
        border: 1px solid #1e3c72;
        background: white;
    }

    .login-button:hover {
        background-color: #1e3c72;
        color: white;
    }

    .signup-button {
        background-color: #1e3c72;
        color: white;
        border: 1px solid #1e3c72;
    }

    .signup-button:hover {
        background-color: #2a5298;
    }

    .job-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 30px;
    }

    .job-main {
        background: white;
        border-radius: 12px;
        padding: 40px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .job-section {
        margin-bottom: 40px;
    }

    .job-section:last-child {
        margin-bottom: 0;
    }

    .section-title {
        font-size: 1.5rem;
        color: #1e3c72;
        margin-bottom: 20px;
        font-weight: 600;
        border-bottom: 2px solid #e3f2fd;
        padding-bottom: 10px;
    }

    .section-content {
        color: #555;
        line-height: 1.8;
        font-size: 1rem;
    }

    .section-content ul {
        padding-left: 20px;
        margin: 15px 0;
    }

    .section-content li {
        margin-bottom: 8px;
    }

    .job-sidebar {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .sidebar-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .sidebar-title {
        font-size: 1.2rem;
        color: #1e3c72;
        margin-bottom: 20px;
        font-weight: 600;
    }

    .job-info-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 10px;
    }

    .info-label {
        font-weight: 500;
        color: #666;
        min-width: 80px;
    }

    .info-value {
        color: #333;
        text-align: right;
        flex: 1;
    }

    .share-buttons {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .share-button {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #495057;
        padding: 10px 15px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .share-button:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    .similar-jobs {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .similar-job-item {
        padding: 15px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .similar-job-item:hover {
        border-color: #1e3c72;
        background-color: #f8f9fa;
    }

    .similar-job-title {
        font-size: 1rem;
        color: #1e3c72;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .similar-job-meta {
        font-size: 0.85rem;
        color: #666;
        margin: 0;
    }

    .no-similar-jobs {
        color: #666;
        font-style: italic;
        text-align: center;
        margin: 0;
    }

    .loading-container,
    .error-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 400px;
        text-align: center;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #1e3c72;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
    }

    .loading-spinner-small {
        width: 16px;
        height: 16px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .error-icon {
        font-size: 4rem;
        margin-bottom: 20px;
    }

    .error-container h2 {
        color: #1e3c72;
        margin-bottom: 10px;
    }

    .error-container p {
        color: #666;
        margin-bottom: 30px;
        max-width: 400px;
    }

    .back-to-jobs-button {
        background-color: #1e3c72;
        color: white;
        text-decoration: none;
        padding: 12px 24px;
        border-radius: 6px;
        font-weight: 500;
        transition: background-color 0.3s ease;
    }

    .back-to-jobs-button:hover {
        background-color: #2a5298;
    }

    .success-toast,
    .error-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 10px;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    }

    .success-toast {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .error-toast {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    @@keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .job-details-container {
            padding: 0 15px;
        }

        .job-header {
            flex-direction: column;
            gap: 20px;
            padding: 25px;
        }

        .job-title {
            font-size: 2rem;
        }

        .job-content {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .job-main {
            padding: 25px;
        }

        .auth-buttons {
            flex-direction: column;
        }

        .success-toast,
        .error-toast {
            right: 15px;
            left: 15px;
            top: 15px;
        }
    }

    @@media (max-width: 480px) {
        .job-header {
            padding: 20px;
        }

        .job-title {
            font-size: 1.8rem;
        }

        .job-main {
            padding: 20px;
        }

        .sidebar-card {
            padding: 20px;
        }

        .job-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
    }
</style>

@code {
    [Parameter] public int JobId { get; set; }
    
    private Vacancy? job;
    private List<Vacancy> similarJobs = new();
    private bool isLoading = true;
    private bool hasApplied = false;
    private bool isApplying = false;
    private bool showSuccessMessage = false;
    private bool showErrorMessage = false;
    private string errorMessage = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadJobDetails();
        await CheckApplicationStatus();
        await LoadSimilarJobs();
        isLoading = false;
    }

    private async Task LoadJobDetails()
    {
        job = await DbContext.Vacancies
            .FirstOrDefaultAsync(v => v.Id == JobId && v.IsActive);
    }

    private async Task CheckApplicationStatus()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            var user = await UserManager.GetUserAsync(authState.User);
            if (user != null)
            {
                var profile = await DbContext.CandidateProfiles
                    .FirstOrDefaultAsync(p => p.ApplicationUserId == user.Id);
                
                if (profile != null)
                {
                    hasApplied = await DbContext.CandidateApplications
                        .AnyAsync(a => a.CandidateProfileId == profile.Id && a.VacancyId == JobId);
                }
            }
        }
    }

    private async Task LoadSimilarJobs()
    {
        if (job != null)
        {
            similarJobs = await DbContext.Vacancies
                .Where(v => v.IsActive && v.Id != JobId && 
                           (v.Department == job.Department || v.Location == job.Location))
                .OrderByDescending(v => v.PostedDate)
                .Take(5)
                .ToListAsync();
        }
    }

    private async Task ApplyForJob()
    {
        if (isApplying) return;
        
        isApplying = true;
        
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = await UserManager.GetUserAsync(authState.User);
            
            if (user == null)
            {
                Navigation.NavigateTo("/Account/Login");
                return;
            }

            var profile = await DbContext.CandidateProfiles
                .FirstOrDefaultAsync(p => p.ApplicationUserId == user.Id);
            
            if (profile == null)
            {
                // Create a basic profile if it doesn't exist
                profile = new CandidateProfile
                {
                    ApplicationUserId = user.Id,
                    FirstName = user.UserName // Temporary, user should complete profile
                };
                DbContext.CandidateProfiles.Add(profile);
                await DbContext.SaveChangesAsync();
            }

            // Check if already applied
            var existingApplication = await DbContext.CandidateApplications
                .FirstOrDefaultAsync(a => a.CandidateProfileId == profile.Id && a.VacancyId == JobId);
            
            if (existingApplication != null)
            {
                errorMessage = "You have already applied for this position.";
                showErrorMessage = true;
                await HideErrorMessage();
                return;
            }

            // Create application
            var application = new CandidateApplication
            {
                CandidateProfileId = profile.Id,
                VacancyId = JobId,
                ApplicationDate = DateTime.UtcNow,
                Status = "Submitted"
            };

            DbContext.CandidateApplications.Add(application);
            await DbContext.SaveChangesAsync();

            hasApplied = true;
            showSuccessMessage = true;
            await HideSuccessMessage();
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred while submitting your application. Please try again.";
            showErrorMessage = true;
            await HideErrorMessage();
        }
        finally
        {
            isApplying = false;
        }
    }

    private async Task HideSuccessMessage()
    {
        await Task.Delay(3000);
        showSuccessMessage = false;
        StateHasChanged();
    }

    private async Task HideErrorMessage()
    {
        await Task.Delay(3000);
        showErrorMessage = false;
        StateHasChanged();
    }

    private void NavigateToJob(int jobId)
    {
        Navigation.NavigateTo($"/job/{jobId}");
    }

    private void ShareJob(string platform)
    {
        var jobUrl = Navigation.Uri;
        var jobTitle = job?.Title ?? "Job Opportunity";
        
        switch (platform.ToLower())
        {
            case "linkedin":
                var linkedInUrl = $"https://www.linkedin.com/sharing/share-offsite/?url={Uri.EscapeDataString(jobUrl)}";
                Navigation.NavigateTo(linkedInUrl, true);
                break;
            case "email":
                var emailSubject = Uri.EscapeDataString($"Job Opportunity: {jobTitle}");
                var emailBody = Uri.EscapeDataString($"I found this job opportunity that might interest you:\n\n{jobTitle}\n{jobUrl}");
                Navigation.NavigateTo($"mailto:?subject={emailSubject}&body={emailBody}", true);
                break;
        }
    }

    private async Task CopyJobLink()
    {
        // Note: In a real application, you would use JavaScript interop to copy to clipboard
        // For now, we'll just show a message
        showSuccessMessage = true;
        await HideSuccessMessage();
    }

    private string GetRelativeTime(DateTime postedDate)
    {
        var timeSpan = DateTime.UtcNow - postedDate;
        
        if (timeSpan.Days > 0)
            return $"{timeSpan.Days} day{(timeSpan.Days > 1 ? "s" : "")} ago";
        else if (timeSpan.Hours > 0)
            return $"{timeSpan.Hours} hour{(timeSpan.Hours > 1 ? "s" : "")} ago";
        else
            return "Recently";
    }

    private string FormatText(string text)
    {
        if (string.IsNullOrEmpty(text))
            return "";
        
        // Convert line breaks to HTML
        text = text.Replace("\n", "<br>");
        
        // Convert bullet points (if they start with - or *)
        var lines = text.Split("<br>");
        var formattedLines = lines.Select(line =>
        {
            line = line.Trim();
            if (line.StartsWith("- ") || line.StartsWith("* "))
            {
                return $"<li>{line.Substring(2)}</li>";
            }
            return line;
        });
        
        var result = string.Join("<br>", formattedLines);
        
        // Wrap consecutive <li> elements in <ul>
        result = System.Text.RegularExpressions.Regex.Replace(result, 
            @"(<li>.*?</li>(?:<br><li>.*?</li>)*)", 
            "<ul>$1</ul>");
        result = result.Replace("<br><li>", "<li>");
        result = result.Replace("</li><br>", "</li>");
        
        return result;
    }
}
