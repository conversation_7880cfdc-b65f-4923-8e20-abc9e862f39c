@page "/internships/technical"
@layout CareerPortal.Components.Layout.CareerPortalLayout

<PageTitle>Technical Internship - Pak Suzuki Career Portal</PageTitle>

<!-- Hero Section -->
<section class="hero">
    <div class="hero-content">
        <h1>Technical Internship Program</h1>
        <p>Get hands-on experience with cutting-edge automotive technology, IT systems, and technical support services at Pakistan's premier automotive company.</p>
        <a href="#apply" class="cta-button">Apply Now</a>
    </div>
</section>

<!-- Program Overview Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Program Overview</h2>
        <p class="section-subtitle">Our Technical Internship Program combines automotive technology with modern IT systems and technical innovation</p>

        <div class="overview-content">
            <div class="overview-text">
                <h3>Technology & Innovation Focus</h3>
                <p>Our technical internship program bridges the gap between traditional automotive technology and modern digital solutions. You'll work with advanced automotive systems, IT infrastructure, and emerging technologies that are shaping the future of mobility.</p>
                
                <h4>What Makes Us Different:</h4>
                <ul class="highlight-list">
                    <li>Exposure to latest automotive technologies</li>
                    <li>Hands-on experience with diagnostic tools</li>
                    <li>IT systems and software development</li>
                    <li>Technical support and troubleshooting</li>
                    <li>Innovation projects and R&D</li>
                    <li>Industry certification opportunities</li>
                </ul>
            </div>
            
            <div class="overview-stats">
                <div class="stat-item">
                    <div class="stat-number">12</div>
                    <div class="stat-label">Weeks Program</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">8</div>
                    <div class="stat-label">Technical Areas</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">95%</div>
                    <div class="stat-label">Skill Improvement</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">30+</div>
                    <div class="stat-label">Tech Projects</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Technical Areas Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Technical Specializations</h2>
        <p class="section-subtitle">Choose your technical track based on your interests and career goals</p>

        <div class="tech-areas-grid">
            <div class="tech-card">
                <div class="tech-icon">🔧</div>
                <h3>Automotive Technology</h3>
                <p>Work with vehicle systems, diagnostic equipment, and automotive maintenance technologies.</p>
                <ul class="tech-features">
                    <li>Engine Management Systems</li>
                    <li>Electronic Control Units (ECU)</li>
                    <li>Diagnostic Tools & Software</li>
                    <li>Vehicle Communication Networks</li>
                </ul>
            </div>

            <div class="tech-card">
                <div class="tech-icon">💻</div>
                <h3>Information Technology</h3>
                <p>Develop and maintain IT systems that support automotive operations and business processes.</p>
                <ul class="tech-features">
                    <li>Software Development</li>
                    <li>Database Management</li>
                    <li>Network Administration</li>
                    <li>System Integration</li>
                </ul>
            </div>

            <div class="tech-card">
                <div class="tech-icon">🛠️</div>
                <h3>Technical Support</h3>
                <p>Provide technical assistance and support for automotive systems and IT infrastructure.</p>
                <ul class="tech-features">
                    <li>Help Desk Operations</li>
                    <li>System Troubleshooting</li>
                    <li>User Training & Support</li>
                    <li>Technical Documentation</li>
                </ul>
            </div>

            <div class="tech-card">
                <div class="tech-icon">🔋</div>
                <h3>Electric Vehicle Technology</h3>
                <p>Explore the future of automotive with electric and hybrid vehicle technologies.</p>
                <ul class="tech-features">
                    <li>Battery Management Systems</li>
                    <li>Electric Motor Control</li>
                    <li>Charging Infrastructure</li>
                    <li>Energy Management</li>
                </ul>
            </div>

            <div class="tech-card">
                <div class="tech-icon">📱</div>
                <h3>Connected Car Systems</h3>
                <p>Work on IoT, telematics, and connected vehicle technologies.</p>
                <ul class="tech-features">
                    <li>Telematics Systems</li>
                    <li>IoT Integration</li>
                    <li>Mobile Applications</li>
                    <li>Data Analytics</li>
                </ul>
            </div>

            <div class="tech-card">
                <div class="tech-icon">🤖</div>
                <h3>Automation & Robotics</h3>
                <p>Experience industrial automation and robotics in automotive manufacturing.</p>
                <ul class="tech-features">
                    <li>Industrial Automation</li>
                    <li>Robotic Systems</li>
                    <li>PLC Programming</li>
                    <li>Process Control</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Tools & Technologies Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Tools & Technologies</h2>
        <p class="section-subtitle">Get hands-on experience with industry-standard tools and cutting-edge technologies</p>

        <div class="tools-categories">
            <div class="tool-category">
                <h3>Automotive Tools</h3>
                <div class="tools-grid">
                    <div class="tool-item">
                        <span class="tool-name">OBD-II Scanners</span>
                        <span class="tool-desc">Vehicle diagnostics</span>
                    </div>
                    <div class="tool-item">
                        <span class="tool-name">Oscilloscopes</span>
                        <span class="tool-desc">Signal analysis</span>
                    </div>
                    <div class="tool-item">
                        <span class="tool-name">Multimeters</span>
                        <span class="tool-desc">Electrical testing</span>
                    </div>
                    <div class="tool-item">
                        <span class="tool-name">CAN Bus Tools</span>
                        <span class="tool-desc">Network diagnostics</span>
                    </div>
                </div>
            </div>

            <div class="tool-category">
                <h3>Software & Programming</h3>
                <div class="tools-grid">
                    <div class="tool-item">
                        <span class="tool-name">Python</span>
                        <span class="tool-desc">Data analysis & automation</span>
                    </div>
                    <div class="tool-item">
                        <span class="tool-name">C/C++</span>
                        <span class="tool-desc">Embedded systems</span>
                    </div>
                    <div class="tool-item">
                        <span class="tool-name">SQL</span>
                        <span class="tool-desc">Database management</span>
                    </div>
                    <div class="tool-item">
                        <span class="tool-name">MATLAB</span>
                        <span class="tool-desc">Engineering analysis</span>
                    </div>
                </div>
            </div>

            <div class="tool-category">
                <h3>Simulation & Design</h3>
                <div class="tools-grid">
                    <div class="tool-item">
                        <span class="tool-name">CANoe</span>
                        <span class="tool-desc">Network simulation</span>
                    </div>
                    <div class="tool-item">
                        <span class="tool-name">LabVIEW</span>
                        <span class="tool-desc">System design</span>
                    </div>
                    <div class="tool-item">
                        <span class="tool-name">Simulink</span>
                        <span class="tool-desc">Model-based design</span>
                    </div>
                    <div class="tool-item">
                        <span class="tool-name">Vector Tools</span>
                        <span class="tool-desc">ECU development</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Learning Path Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">12-Week Learning Journey</h2>
        <p class="section-subtitle">Structured learning path designed to maximize your technical growth</p>

        <div class="learning-timeline">
            <div class="timeline-item">
                <div class="timeline-marker">1-3</div>
                <div class="timeline-content">
                    <h4>Foundation & Orientation</h4>
                    <p>Introduction to automotive systems, safety protocols, and basic technical concepts.</p>
                    <ul>
                        <li>Automotive industry overview</li>
                        <li>Safety and quality standards</li>
                        <li>Basic diagnostic procedures</li>
                    </ul>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-marker">4-6</div>
                <div class="timeline-content">
                    <h4>Hands-on Technical Training</h4>
                    <p>Practical experience with tools, equipment, and real automotive systems.</p>
                    <ul>
                        <li>Diagnostic tool operation</li>
                        <li>System troubleshooting</li>
                        <li>Technical documentation</li>
                    </ul>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-marker">7-9</div>
                <div class="timeline-content">
                    <h4>Specialized Projects</h4>
                    <p>Work on specific projects in your chosen technical specialization area.</p>
                    <ul>
                        <li>Independent project work</li>
                        <li>Advanced system analysis</li>
                        <li>Innovation initiatives</li>
                    </ul>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-marker">10-12</div>
                <div class="timeline-content">
                    <h4>Capstone & Presentation</h4>
                    <p>Complete a capstone project and present your findings to technical teams.</p>
                    <ul>
                        <li>Project completion</li>
                        <li>Results presentation</li>
                        <li>Career planning</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Requirements Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Eligibility & Requirements</h2>
        
        <div class="requirements-content">
            <div class="req-column">
                <h3>Academic Background</h3>
                <ul class="req-list">
                    <li>Computer Science, IT, Electronics, or related technical field</li>
                    <li>Minimum CGPA of 3.0 or equivalent</li>
                    <li>Completed at least 4 semesters</li>
                    <li>Available for 12-week full-time internship</li>
                </ul>
            </div>

            <div class="req-column">
                <h3>Technical Skills</h3>
                <ul class="req-list">
                    <li>Basic programming knowledge (any language)</li>
                    <li>Understanding of computer systems</li>
                    <li>Problem-solving abilities</li>
                    <li>Willingness to learn new technologies</li>
                </ul>
            </div>

            <div class="req-column">
                <h3>Personal Attributes</h3>
                <ul class="req-list">
                    <li>Strong analytical thinking</li>
                    <li>Attention to detail</li>
                    <li>Team collaboration skills</li>
                    <li>Curiosity about technology</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Application Section -->
<section class="section cta-section" id="apply">
    <div class="container">
        <div class="cta-content">
            <h2>Shape the Future of Automotive Technology</h2>
            <p>Join our technical internship program and be part of the technological revolution in Pakistan's automotive industry.</p>
            <div class="cta-buttons">
                <a href="/Account/Register" class="cta-button">Apply Now</a>
                <a href="/internships" class="cta-button-secondary">View All Programs</a>
            </div>
        </div>
    </div>
</section>

<style>
    /* Base styles similar to other internship pages */
    .hero {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 80px 20px;
        text-align: center;
        position: relative;
        min-height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 800px;
    }

    .hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        color: white;
    }

    .hero p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-button {
        display: inline-block;
        background: #ff6b35;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    }

    .cta-button:hover {
        background: #e55a2b;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        color: white;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section {
        padding: 60px 0;
        width: 100%;
    }

    .section-title {
        text-align: center;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .section-subtitle {
        text-align: center;
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 50px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .overview-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 50px;
        align-items: start;
    }

    .overview-text h3 {
        font-size: 1.8rem;
        color: #1e3c72;
        margin-bottom: 20px;
    }

    .overview-text h4 {
        font-size: 1.3rem;
        color: #1e3c72;
        margin: 25px 0 15px 0;
    }

    .overview-text p {
        color: #666;
        line-height: 1.7;
        margin-bottom: 20px;
    }

    .highlight-list {
        list-style: none;
        padding-left: 0;
    }

    .highlight-list li {
        padding: 8px 0;
        color: #555;
        position: relative;
        padding-left: 25px;
    }

    .highlight-list li:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .overview-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .stat-item {
        text-align: center;
        padding: 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #ff6b35;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
    }

    .tech-areas-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .tech-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: 1px solid #f0f0f0;
    }

    .tech-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .tech-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        font-size: 1.8rem;
        color: white;
    }

    .tech-card h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #1e3c72;
    }

    .tech-card p {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .tech-features {
        list-style: none;
        padding-left: 0;
    }

    .tech-features li {
        padding: 5px 0;
        color: #555;
        position: relative;
        padding-left: 20px;
        font-size: 0.9rem;
    }

    .tech-features li:before {
        content: "•";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .tools-categories {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 40px;
        margin-top: 40px;
    }

    .tool-category h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 25px;
        color: #1e3c72;
        text-align: center;
    }

    .tools-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .tool-item {
        background: white;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        text-align: center;
        transition: transform 0.3s ease;
    }

    .tool-item:hover {
        transform: translateY(-3px);
    }

    .tool-name {
        display: block;
        font-weight: 600;
        color: #1e3c72;
        margin-bottom: 5px;
    }

    .tool-desc {
        font-size: 0.8rem;
        color: #666;
    }

    .learning-timeline {
        max-width: 800px;
        margin: 40px auto 0;
    }

    .timeline-item {
        display: flex;
        gap: 30px;
        margin-bottom: 40px;
        align-items: flex-start;
    }

    .timeline-marker {
        background: #1e3c72;
        color: white;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.9rem;
        flex-shrink: 0;
    }

    .timeline-content {
        flex: 1;
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .timeline-content h4 {
        font-size: 1.3rem;
        font-weight: 600;
        color: #1e3c72;
        margin-bottom: 10px;
    }

    .timeline-content p {
        color: #666;
        margin-bottom: 15px;
        line-height: 1.6;
    }

    .timeline-content ul {
        list-style: none;
        padding-left: 0;
    }

    .timeline-content li {
        padding: 3px 0;
        color: #555;
        position: relative;
        padding-left: 20px;
        font-size: 0.9rem;
    }

    .timeline-content li:before {
        content: "→";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .requirements-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        margin-top: 40px;
    }

    .req-column h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .req-list {
        list-style: none;
        padding-left: 0;
    }

    .req-list li {
        padding: 10px 0;
        color: #555;
        position: relative;
        padding-left: 25px;
        line-height: 1.5;
    }

    .req-list li:before {
        content: "→";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .cta-section {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
    }

    .cta-content {
        text-align: center;
        max-width: 600px;
        margin: 0 auto;
    }

    .cta-content h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: white;
    }

    .cta-content p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .cta-button-secondary {
        display: inline-block;
        background: transparent;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        border: 2px solid white;
        transition: all 0.3s ease;
    }

    .cta-button-secondary:hover {
        background: white;
        color: #1e3c72;
    }

    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .hero {
            padding: 60px 15px;
            min-height: 350px;
        }
        
        .hero h1 {
            font-size: 2.5rem;
        }
        
        .section {
            padding: 40px 0;
        }
        
        .section-title {
            font-size: 2rem;
        }
        
        .overview-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .overview-stats {
            grid-template-columns: 1fr 1fr;
        }
        
        .tech-areas-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .tools-categories {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .timeline-item {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }
        
        .requirements-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .container {
            padding: 0 15px;
        }

        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }
    }

    @@media (max-width: 480px) {
        .hero h1 {
            font-size: 2rem;
        }
        
        .section-title {
            font-size: 1.8rem;
        }
        
        .overview-stats {
            grid-template-columns: 1fr;
        }

        .cta-content h2 {
            font-size: 2rem;
        }

        .tools-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
