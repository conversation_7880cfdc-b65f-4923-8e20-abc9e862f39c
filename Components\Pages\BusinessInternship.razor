@page "/internships/business"
@layout CareerPortal.Components.Layout.CareerPortalLayout

<PageTitle>Business Internship - Pak Suzuki Career Portal</PageTitle>

<!-- Hero Section -->
<section class="hero">
    <div class="hero-content">
        <h1>Business Internship Program</h1>
        <p>Develop your business acumen through hands-on experience in sales, marketing, finance, and operations at Pakistan's leading automotive company.</p>
        <a href="#apply" class="cta-button">Apply Now</a>
    </div>
</section>

<!-- Program Overview Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Program Overview</h2>
        <p class="section-subtitle">Our Business Internship Program provides comprehensive exposure to various business functions in the automotive industry</p>

        <div class="overview-content">
            <div class="overview-text">
                <h3>Business Excellence Journey</h3>
                <p>Our business internship program is designed to give you real-world experience across multiple business functions. You'll work on strategic projects, analyze market trends, and contribute to business growth initiatives while learning from industry experts.</p>
                
                <h4>Program Benefits:</h4>
                <ul class="highlight-list">
                    <li>Cross-functional business exposure</li>
                    <li>Strategic project involvement</li>
                    <li>Market analysis and research</li>
                    <li>Client interaction opportunities</li>
                    <li>Business presentation skills</li>
                    <li>Leadership development workshops</li>
                </ul>
            </div>
            
            <div class="overview-stats">
                <div class="stat-item">
                    <div class="stat-number">10</div>
                    <div class="stat-label">Weeks Duration</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">6</div>
                    <div class="stat-label">Business Functions</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">90%</div>
                    <div class="stat-label">Satisfaction Rate</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">Business Projects</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Business Functions Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Business Functions</h2>
        <p class="section-subtitle">Gain exposure to key business areas that drive our automotive business</p>

        <div class="functions-grid">
            <div class="function-card">
                <div class="func-icon">📈</div>
                <h3>Sales & Marketing</h3>
                <p>Learn sales strategies, customer relationship management, and marketing campaigns in the automotive sector.</p>
                <ul class="func-features">
                    <li>Sales Process Management</li>
                    <li>Customer Relationship Building</li>
                    <li>Digital Marketing Campaigns</li>
                    <li>Market Research & Analysis</li>
                </ul>
            </div>

            <div class="function-card">
                <div class="func-icon">💰</div>
                <h3>Finance & Accounting</h3>
                <p>Understand financial planning, budgeting, cost analysis, and financial reporting in automotive business.</p>
                <ul class="func-features">
                    <li>Financial Analysis</li>
                    <li>Budget Planning</li>
                    <li>Cost Management</li>
                    <li>Financial Reporting</li>
                </ul>
            </div>

            <div class="function-card">
                <div class="func-icon">⚙️</div>
                <h3>Operations Management</h3>
                <p>Experience supply chain management, logistics, and operational efficiency in automotive manufacturing.</p>
                <ul class="func-features">
                    <li>Supply Chain Optimization</li>
                    <li>Process Improvement</li>
                    <li>Inventory Management</li>
                    <li>Quality Systems</li>
                </ul>
            </div>

            <div class="function-card">
                <div class="func-icon">👥</div>
                <h3>Human Resources</h3>
                <p>Learn talent management, recruitment, training, and employee development strategies.</p>
                <ul class="func-features">
                    <li>Talent Acquisition</li>
                    <li>Employee Development</li>
                    <li>Performance Management</li>
                    <li>HR Analytics</li>
                </ul>
            </div>

            <div class="function-card">
                <div class="func-icon">🎯</div>
                <h3>Strategic Planning</h3>
                <p>Participate in strategic initiatives, business planning, and market expansion projects.</p>
                <ul class="func-features">
                    <li>Business Strategy Development</li>
                    <li>Market Analysis</li>
                    <li>Competitive Intelligence</li>
                    <li>Growth Planning</li>
                </ul>
            </div>

            <div class="function-card">
                <div class="func-icon">🤝</div>
                <h3>Customer Service</h3>
                <p>Understand customer experience management and service excellence in automotive industry.</p>
                <ul class="func-features">
                    <li>Customer Experience Design</li>
                    <li>Service Quality Management</li>
                    <li>Customer Feedback Analysis</li>
                    <li>Service Innovation</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Skills Development Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Skills You'll Develop</h2>
        <p class="section-subtitle">Build essential business skills that will accelerate your career</p>

        <div class="skills-grid">
            <div class="skill-category">
                <h3>Analytical Skills</h3>
                <div class="skills-list">
                    <span class="skill-tag">Data Analysis</span>
                    <span class="skill-tag">Market Research</span>
                    <span class="skill-tag">Financial Modeling</span>
                    <span class="skill-tag">Business Intelligence</span>
                    <span class="skill-tag">Problem Solving</span>
                </div>
            </div>

            <div class="skill-category">
                <h3>Communication Skills</h3>
                <div class="skills-list">
                    <span class="skill-tag">Presentation Skills</span>
                    <span class="skill-tag">Business Writing</span>
                    <span class="skill-tag">Client Communication</span>
                    <span class="skill-tag">Negotiation</span>
                    <span class="skill-tag">Public Speaking</span>
                </div>
            </div>

            <div class="skill-category">
                <h3>Leadership Skills</h3>
                <div class="skills-list">
                    <span class="skill-tag">Team Management</span>
                    <span class="skill-tag">Project Leadership</span>
                    <span class="skill-tag">Decision Making</span>
                    <span class="skill-tag">Strategic Thinking</span>
                    <span class="skill-tag">Change Management</span>
                </div>
            </div>

            <div class="skill-category">
                <h3>Technical Skills</h3>
                <div class="skills-list">
                    <span class="skill-tag">Excel & Analytics</span>
                    <span class="skill-tag">CRM Systems</span>
                    <span class="skill-tag">ERP Software</span>
                    <span class="skill-tag">Digital Marketing</span>
                    <span class="skill-tag">Business Software</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Examples Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Real Project Examples</h2>
        <p class="section-subtitle">See the types of impactful projects our business interns work on</p>

        <div class="projects-grid">
            <div class="project-card">
                <div class="project-header">
                    <h4>Market Expansion Analysis</h4>
                    <span class="project-type">Strategic Planning</span>
                </div>
                <p>Analyze potential new markets for Suzuki vehicles, including demographic research, competitive analysis, and market entry strategies.</p>
                <div class="project-impact">
                    <strong>Impact:</strong> Recommendations led to successful launch in 2 new cities
                </div>
            </div>

            <div class="project-card">
                <div class="project-header">
                    <h4>Customer Satisfaction Initiative</h4>
                    <span class="project-type">Customer Service</span>
                </div>
                <p>Design and implement customer feedback system to improve service quality and customer experience across dealerships.</p>
                <div class="project-impact">
                    <strong>Impact:</strong> 25% improvement in customer satisfaction scores
                </div>
            </div>

            <div class="project-card">
                <div class="project-header">
                    <h4>Digital Marketing Campaign</h4>
                    <span class="project-type">Marketing</span>
                </div>
                <p>Develop and execute digital marketing strategy for new vehicle launch, including social media, content marketing, and online advertising.</p>
                <div class="project-impact">
                    <strong>Impact:</strong> 40% increase in online engagement and leads
                </div>
            </div>

            <div class="project-card">
                <div class="project-header">
                    <h4>Cost Optimization Study</h4>
                    <span class="project-type">Operations</span>
                </div>
                <p>Analyze operational costs and identify opportunities for efficiency improvements in manufacturing and supply chain processes.</p>
                <div class="project-impact">
                    <strong>Impact:</strong> Identified savings of PKR 50M annually
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Requirements Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Eligibility & Requirements</h2>
        
        <div class="requirements-content">
            <div class="req-column">
                <h3>Academic Background</h3>
                <ul class="req-list">
                    <li>Business Administration, Management, Economics, or related field</li>
                    <li>Minimum CGPA of 3.0 or equivalent</li>
                    <li>Completed at least 4 semesters</li>
                    <li>Available for 10-week internship</li>
                </ul>
            </div>

            <div class="req-column">
                <h3>Skills & Competencies</h3>
                <ul class="req-list">
                    <li>Strong analytical and quantitative skills</li>
                    <li>Excellent communication abilities</li>
                    <li>Proficiency in MS Office Suite</li>
                    <li>Basic understanding of business principles</li>
                </ul>
            </div>

            <div class="req-column">
                <h3>Personal Qualities</h3>
                <ul class="req-list">
                    <li>Leadership potential and initiative</li>
                    <li>Customer-focused mindset</li>
                    <li>Adaptability and learning agility</li>
                    <li>Professional attitude and ethics</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Application Section -->
<section class="section cta-section" id="apply">
    <div class="container">
        <div class="cta-content">
            <h2>Launch Your Business Career</h2>
            <p>Join our business internship program and gain the experience and skills needed to excel in the automotive industry.</p>
            <div class="cta-buttons">
                <a href="/Account/Register" class="cta-button">Apply Now</a>
                <a href="/internships" class="cta-button-secondary">View All Programs</a>
            </div>
        </div>
    </div>
</section>

<style>
    /* Base styles similar to other internship pages */
    .hero {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 80px 20px;
        text-align: center;
        position: relative;
        min-height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        max-width: 800px;
    }

    .hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        color: white;
    }

    .hero p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-button {
        display: inline-block;
        background: #ff6b35;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    }

    .cta-button:hover {
        background: #e55a2b;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        color: white;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section {
        padding: 60px 0;
        width: 100%;
    }

    .section-title {
        text-align: center;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .section-subtitle {
        text-align: center;
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 50px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .overview-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 50px;
        align-items: start;
    }

    .overview-text h3 {
        font-size: 1.8rem;
        color: #1e3c72;
        margin-bottom: 20px;
    }

    .overview-text h4 {
        font-size: 1.3rem;
        color: #1e3c72;
        margin: 25px 0 15px 0;
    }

    .overview-text p {
        color: #666;
        line-height: 1.7;
        margin-bottom: 20px;
    }

    .highlight-list {
        list-style: none;
        padding-left: 0;
    }

    .highlight-list li {
        padding: 8px 0;
        color: #555;
        position: relative;
        padding-left: 25px;
    }

    .highlight-list li:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .overview-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .stat-item {
        text-align: center;
        padding: 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #ff6b35;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
    }

    .functions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .function-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: 1px solid #f0f0f0;
    }

    .function-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .func-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        font-size: 1.8rem;
        color: white;
    }

    .function-card h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #1e3c72;
    }

    .function-card p {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .func-features {
        list-style: none;
        padding-left: 0;
    }

    .func-features li {
        padding: 5px 0;
        color: #555;
        position: relative;
        padding-left: 20px;
        font-size: 0.9rem;
    }

    .func-features li:before {
        content: "•";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .skills-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .skill-category {
        background: white;
        border-radius: 10px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }

    .skill-category h3 {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .skills-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .skill-tag {
        background: #f8f9fa;
        color: #1e3c72;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
        border: 1px solid #e9ecef;
    }

    .projects-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-top: 40px;
    }

    .project-card {
        background: white;
        border-radius: 10px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        border-left: 4px solid #ff6b35;
    }

    .project-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .project-header h4 {
        font-size: 1.2rem;
        font-weight: 600;
        color: #1e3c72;
        margin: 0;
    }

    .project-type {
        background: #e8f4f8;
        color: #1e3c72;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .project-card p {
        color: #666;
        line-height: 1.6;
        margin-bottom: 15px;
    }

    .project-impact {
        color: #28a745;
        font-size: 0.9rem;
        padding: 10px;
        background: #f8fff9;
        border-radius: 5px;
        border-left: 3px solid #28a745;
    }

    .requirements-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        margin-top: 40px;
    }

    .req-column h3 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: #1e3c72;
    }

    .req-list {
        list-style: none;
        padding-left: 0;
    }

    .req-list li {
        padding: 10px 0;
        color: #555;
        position: relative;
        padding-left: 25px;
        line-height: 1.5;
    }

    .req-list li:before {
        content: "→";
        position: absolute;
        left: 0;
        color: #ff6b35;
        font-weight: bold;
    }

    .cta-section {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
    }

    .cta-content {
        text-align: center;
        max-width: 600px;
        margin: 0 auto;
    }

    .cta-content h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        color: white;
    }

    .cta-content p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.95;
        color: white;
    }

    .cta-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .cta-button-secondary {
        display: inline-block;
        background: transparent;
        color: white;
        padding: 15px 35px;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        border: 2px solid white;
        transition: all 0.3s ease;
    }

    .cta-button-secondary:hover {
        background: white;
        color: #1e3c72;
    }

    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .hero {
            padding: 60px 15px;
            min-height: 350px;
        }
        
        .hero h1 {
            font-size: 2.5rem;
        }
        
        .section {
            padding: 40px 0;
        }
        
        .section-title {
            font-size: 2rem;
        }
        
        .overview-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .overview-stats {
            grid-template-columns: 1fr 1fr;
        }
        
        .functions-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .skills-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .projects-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .requirements-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .container {
            padding: 0 15px;
        }

        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }
    }

    @@media (max-width: 480px) {
        .hero h1 {
            font-size: 2rem;
        }
        
        .section-title {
            font-size: 1.8rem;
        }
        
        .overview-stats {
            grid-template-columns: 1fr;
        }

        .cta-content h2 {
            font-size: 2rem;
        }

        .project-header {
            flex-direction: column;
            align-items: flex-start;
        }
    }
</style>
